{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\lang\\en.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\lang\\en.js", "mtime": 1754550261876}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _common, _product, _search, _request, _history, _admin, _affiliateProducts, _common$navbar$common;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar _default = exports.default = (_common$navbar$common = {\n  common: {\n    confirm: \"Confirm\",\n    cancel: \"Cancel\",\n    tip: \"Tip\",\n    cancelled: \"Cancelled\",\n    deleteFile: \"Permanently delete this file\",\n    systemTip: \"System Tip\"\n  },\n  navbar: {\n    home: \"Home\",\n    profile: \"Profile\",\n    logout: \"Logout\"\n  }\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"common\", (_common = {\n  editSuccess: \"Updated successfully\",\n  addSuccess: \"Added successfully\",\n  confirmDelete: \"Are you sure to delete the item named '{name}'?\",\n  status: \"Status\",\n  fetchDataFailed: \"Failed to fetch data\",\n  operationSuccess: \"Operation succeeded\",\n  operationFailed: \"Operation failed\",\n  unknownError: \"Unknown Error\",\n  confirm: \"Confirm\",\n  cancel: \"Cancel\",\n  deleteConfirm: \"Are you sure to delete?\",\n  deleteSuccess: \"Deleted successfully\",\n  deleteFailed: \"Delete failed\",\n  saveSuccess: \"Saved successfully\",\n  saveFailed: \"Save failed\",\n  enterRejectReason: \"Please enter reject reason\",\n  pendingReview: \"Pending Review\",\n  reviewedRejected: \"Rejected\",\n  reviewedPassed: \"Approved\",\n  unknown: \"Unknown\",\n  startDate: \"Start Date\",\n  endDate: \"End Date\",\n  all: \"All\",\n  serialNumber: \"Serial Number\",\n  query: \"Query\",\n  reset: \"Reset\",\n  enter: \"Please enter\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common, \"reviewedPassed\", \"Reviewed Passed\"), \"reviewedRejected\", \"Reviewed Rejected\"), \"pleaseSelect\", \"Please Select\"), \"yes\", \"Yes\"), \"no\", \"No\"), \"show\", \"Show\"), \"hide\", \"Hide\"), \"unknown\", \"Unknown\"), \"keyword\", {\n  text: \"Text Message\",\n  image: \"Image Message\",\n  news: \"News Message\",\n  voice: \"Voice Message\"\n}), \"couponType\", {\n  general: \"General Coupon\",\n  product: \"Product Coupon\",\n  category: \"Category Coupon\"\n}), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common, \"couponReceive\", {\n  manual: \"Manual Claim\",\n  newUser: \"New User Coupon\",\n  gift: \"Gift Coupon\"\n}), \"paymentStatus\", {\n  unpaid: \"Unpaid\",\n  paid: \"Paid\"\n}), \"withdrawType\", {\n  bank: \"Bank Card\",\n  alipay: \"Alipay\",\n  wechat: \"WeChat\"\n}), \"rechargeType\", {\n  wechatPublic: \"WeChat Official Account\",\n  wechatH5: \"WeChat H5 Payment\",\n  miniProgram: \"Mini Program\"\n}), \"withdrawStatus\", {\n  rejected: \"Rejected\",\n  reviewing: \"Under Review\",\n  withdrawn: \"Withdrawn\"\n}), \"status\", {\n  bargain: {\n    1: \"In Progress\",\n    2: \"Unfinished\",\n    3: \"Completed\"\n  }\n}), \"onePass\", {\n  sms: \"SMS\",\n  copy: \"Product Collection\",\n  expr_query: \"Logistics Inquiry\",\n  expr_dump: \"E-waybill Printing\"\n}), \"editStatus\", {\n  1: \"Unreviewed\",\n  2: \"Under Review\",\n  3: \"Review Failed\",\n  4: \"Reviewed Successfully\"\n}), \"videoStatus\", {\n  0: \"Initial Value\",\n  5: \"Published\",\n  11: \"Manually Unpublished\",\n  13: \"Violation Removed / Risk Control Removed\"\n}), \"actions\", \"Actions\"))), \"appMain\", {\n  copyright: \"Copyright © 2025\"\n}), \"dashboard\", {\n  home: \"Home\",\n  brandCenter: \"Brand Center\",\n  brandManage: \"Brand Management\",\n  productManage: \"Product Management\",\n  appManage: \"App Management\",\n  homeManage: \"Homepage Management\",\n  opsCenter: \"Operations Center\",\n  affiliateProducts: \"Affiliate Products\",\n  withdrawalReview: \"Withdrawal Review\",\n  withdrawalRecords: \"Withdrawal Records\",\n  orderCenter: \"Order Center\",\n  orderInquiry: \"Order Inquiry\",\n  userCenter: \"User Center\",\n  userManage: \"User Management\",\n  userLevel: \"User Level\",\n  levelUpgradeOrder: \"Level Upgrade Order\",\n  financeCenter: \"Finance Center\",\n  financeDetails: \"Financial Details\",\n  withdrawalRequest: \"Withdrawal Request\",\n  paramSettings: \"Parameter Settings\",\n  rewardRules: \"Reward Rules Setup\",\n  withdrawalFee: \"Withdrawal Fee Setup\",\n  referralRewardConfig: \"Referral Reward Configuration\",\n  membershipFee: \"Membership Upgrade Fee Setup\",\n  accountCenter: \"Account Center\",\n  adminPermissions: \"Admin Permissions\",\n  roleManage: \"Role Management\",\n  adminList: \"Admin List\",\n  permissionRules: \"Permission Rules\",\n  profile: \"Profile\",\n  systemSettings: \"System settings\",\n  chainTransferRecord: \"Chain transfer record\",\n  platformCashbackRate: \"Platform Cashback Rate Settings\",\n  shoppingCashbackRules: \"Shopping Cashback Rules\"\n}), \"platformCashbackRate\", {\n  platformCashbackRate: \"Tingkat pengembalian platform\",\n  editTitle: \"Edit Tingkat Pengembalian Platform\",\n  addTitle: \"Tambah Tingkat Pengembalian Platform\",\n  placeholder: {\n    platformCashbackRate: \"Masukkan tingkat pengembalian platform\"\n  }\n}), \"tagsView\", {\n  refresh: \"Refresh\",\n  close: \"Close\",\n  closeOthers: \"Close Others\",\n  closeAll: \"Close All\"\n}), \"homepage\", {\n  welcome: \"Welcome to the GENCO Admin Panel!\",\n  paymentSwitch: \"Payment Switch\",\n  paymentSwitchTip1: \"When enabled, the frontend allows users to become agents and partners.\",\n  paymentSwitchTip2: \"When disabled, upgrades are not allowed. This is only for AppStore submission.\",\n  paymentSwitchTip3: \"Do not operate casually.\",\n  loginMode: \"Login Method\",\n  loginModeTip1: \"This setting only controls the display of TikTok and SMS login options\",\n  loginModeTip2: \"on the login page for AppStore submission. Do not operate casually.\",\n  tikTokLogin: \"TikTok Login\",\n  smsLogin: \"SMS Login\",\n  submit: \"Submit\"\n}), \"brand\", {\n  search: \"Brand Search:\",\n  status: \"Status:\",\n  pleaseSelect: \"Please Select\",\n  reset: \"Reset\",\n  query: \"Query\",\n  addBrand: \"Add Brand\",\n  batchOnline: \"Batch Online\",\n  batchOffline: \"Batch Offline\",\n  batchDelete: \"Batch Delete\",\n  brandLogo: \"Brand Logo\",\n  brandName: \"Brand Name\",\n  industry: \"Industry\",\n  platform: \"Platform\",\n  productCount: \"Product Count\",\n  maxCashback: \"Max Cashback Rate\",\n  soldCount: \"Sold Count\",\n  soldAmount: \"Sold Amount (Rp)\",\n  cashbackAmount: \"Cashback Amount (Rp)\",\n  shareCount: \"Share Count\",\n  createTime: \"Create Time\",\n  creator: \"Creator\",\n  statusLabel: \"Status\",\n  isHot: \"Is Hot Brand\",\n  isHighCashback: \"Is High Cashback Brand\",\n  offline: \"Offline\",\n  online: \"Online\",\n  edit: \"Edit\",\n  delete: \"Delete\",\n  addDialogTitle: \"Add Brand\",\n  editDialogTitle: \"Edit Brand\",\n  brandNameInput: \"Enter brand name\",\n  brandLogoInput: \"Enter image URL\",\n  contactPerson: \"Contact Person:\",\n  contactPhone: \"Contact Phone:\",\n  confirm: \"Confirm\",\n  update: \"Update\",\n  cancel: \"Cancel\",\n  platformTiktok: \"TikTok\",\n  platformShopee: \"Shopee\",\n  confirmOperation: \"Are you sure to perform this operation?\",\n  prompt: \"Prompt\",\n  productList: \"Product\",\n  isOnline: \"On Sale\",\n  isOutline: \"Pending\",\n  isOuted: \"Offline\",\n  isDeleted: \"Deleted\",\n  unknown: \"Unknown\",\n  selectTip: \"Please select\",\n  refreshBrands: \"Refresh Brand Data\",\n  refreshingBrands: \"Refreshing brand data...\"\n}), \"product\", (_product = {\n  search: \"Product Search:\",\n  keywordsPlaceholder: \"Enter product name or keywords\",\n  status: \"Status:\",\n  pleaseSelect: \"Please Select\",\n  query: \"Query\",\n  reset: \"Reset\",\n  addProduct: \"Add Product\",\n  batchOnline: \"Batch Online\",\n  batchOffline: \"Batch Offline\",\n  batchDelete: \"Batch Delete\",\n  productImage: \"Product Image\",\n  productName: \"Product Name\",\n  productPrice: \"Product Price\",\n  cashbackRate: \"Cashback Rate\",\n  estimatedCashback: \"Estimated Cashback (Rp)\",\n  productLink: \"Product Link\",\n  shareCount: \"Share Count\",\n  soldCount: \"Sold Count\",\n  cashbackAmount: \"Cashback Amount (Rp)\",\n  addTime: \"Add Time\",\n  action: \"Action\",\n  offline: \"Offline\",\n  online: \"Online\",\n  edit: \"Edit\",\n  delete: \"Delete\",\n  isHot: \"Hot\",\n  isBenefit: \"High Cashback\",\n  isTikTok: \"TikTok\",\n  addDialogTitle: \"Add Product\",\n  editDialogTitle: \"Edit Product\",\n  enterProductLink: \"Enter product link\",\n  fetchProductInfo: \"Fetch Product Info\",\n  enterProductName: \"Enter product name\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"productPrice\", \"Product Price\"), \"enterProductPrice\", \"Enter product price\"), \"enterCashbackRate\", \"Enter cashback rate\"), \"enterCashbackAmount\", \"Enter cashback amount\"), \"isOnline\", \"Is Online:\"), \"yes\", \"Yes\"), \"no\", \"No\"), \"confirm\", \"Confirm\"), \"cancel\", \"Cancel\"), \"all\", \"All\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"fetchProductFailed\", \"Failed to fetch product info\"), \"isOnIndex\", \"Display on the homepage?\"), \"usercashbackRate\", \"User Cashback Rate\"), \"isOnline\", \"On Sale\"), \"isOutline\", \"Pending\"), \"isOuted\", \"Offline\"))), \"operations\", {\n  withdrawal: {\n    walletWithdrawal: \"Wallet Withdrawal\",\n    bankWithdrawal: \"Bank Withdrawal\",\n    applicant: \"Applicant\",\n    applicationTime: \"Application Time\",\n    electronicWallet: \"Electronic Wallet\",\n    bankName: \"Bank Name\",\n    applicationId: \"Application ID\",\n    applicantName: \"Applicant\",\n    withdrawalAmount: \"Withdrawal Amount\",\n    serviceFee: \"Service Fee\",\n    actualAmount: \"Actual Amount\",\n    walletCode: \"Wallet Code\",\n    walletAccount: \"Account\",\n    bankCardNumber: \"Bank Card Number\",\n    name: \"Name\",\n    phoneNumber: \"Phone Number\",\n    withdrawalCount: \"Withdrawal Count\",\n    auditResult: \"Audit Result\",\n    rejectReason: \"Reject Reason\",\n    approve: \"Approve\",\n    reject: \"Reject\",\n    rejectReview: \"Reject Review\",\n    approveReview: \"Approve Review\",\n    auditOperation: \"Audit Operation\",\n    auditStatus: \"Audit Status\",\n    transactionNumber: \"Transaction Number\",\n    transactionRemark: \"Transaction Remark\",\n    enterTransactionNumber: \"Please enter transaction number\",\n    enterTransactionRemark: \"Please enter transaction remark (optional)\",\n    enterRejectReason: \"Please enter reject reason\",\n    transactionNumberRequired: \"Please enter transaction number\",\n    rejectReasonRequired: \"Please enter reject reason\",\n    exportExcel: \"Export Excel\",\n    transferTime: \"Transfer Time\",\n    transferResult: \"Transfer Result\",\n    remark: \"Remark\",\n    attachment: \"Attachment\",\n    operator: \"Operator\",\n    withdrawalStatus: \"Withdrawal Status\",\n    ShopeePay: \"ShopeePay\",\n    DANA: \"DANA\",\n    OVO: \"OVO\",\n    Gopay: \"Gopay\",\n    unapproved: \"Unapproved\",\n    underReview: \"Under Review\",\n    reviewed: \"Reviewed\",\n    paid: \"Paid\"\n  }\n}), \"order\", {\n  search: (_search = {\n    orderNo: \"Order Number\",\n    productTitle: \"Product Name\",\n    status: \"Status\",\n    all: \"All\",\n    query: \"Query\",\n    reset: \"Reset\",\n    exportExcel: \"Export Excel\",\n    serialNumber: \"Serial Number\",\n    image: \"Product Image\",\n    orderId: \"Order ID\",\n    productName: \"Product Name\",\n    payCount: \"Purchase Count\",\n    actualCommission: \"Product Price (Rp)\",\n    payPrice: \"Order Amount (Rp)\",\n    commissionRate: \"Product Cashback Rate\",\n    estimatedCommission: \"Estimated Cashback (Rp)\",\n    contentId: \"E-commerce Platform\",\n    statusLabel: \"Order Status\",\n    unknown: \"Unknown\",\n    ordered: \"Ordered\",\n    settled: \"Settled\",\n    refunded: \"Refunded\",\n    frozen: \"Frozen\",\n    deducted: \"Deducted\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_search, \"all\", \"All\"), \"unPaid\", \"Unpaid\"), \"notShipped\", \"Not Shipped\"), \"spike\", \"Pending Receipt\"), \"bargain\", \"Pending Review\"), \"complete\", \"Completed\"), \"toBeWrittenOff\", \"To Be Written Off\"), \"refunding\", \"Refunding\"), \"refunded\", \"Refunded\"), \"deleted\", \"Deleted\"), _defineProperty(_defineProperty(_defineProperty(_search, \"totalPrice\", \"Amount\"), \"userCashBackRate\", \"User Cashback Rate\"), \"creatTime\", \"Order Time\"))\n}), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"user\", {\n  center: {\n    nickname: \"Nickname\",\n    phone: \"Phone Number\",\n    userLevel: \"User Level\",\n    query: \"Query\",\n    reset: \"Reset\",\n    serialNumber: \"Serial Number\",\n    avatar: \"Avatar\",\n    tiktokAccount: \"TikTok Account\",\n    tiktokId: \"TikTok ID\",\n    whatsApp: \"WhatsApp\",\n    registerTime: \"Registration Time\",\n    lastLoginTime: \"Last Login Time\",\n    orderCount: \"Order Count\",\n    orderFinishCount: \"Order Finish Count\",\n    isAgent: \"Is Agent\",\n    isPartner: \"Is Partner\",\n    userLevelLabel: \"User Level\",\n    inviter: \"Inviter\",\n    userTags: \"User Tags\"\n  },\n  levelUpgrade: {\n    title: \"Level Upgrade Order Management\",\n    orderNo: \"Order No.\",\n    userId: \"User ID\",\n    upgradeInfo: \"Upgrade Info\",\n    upgradeFee: \"Upgrade Fee\",\n    paymentMethod: \"Payment Method\",\n    orderStatus: \"Order Status\",\n    createTime: \"Create Time\",\n    payTime: \"Payment Time\",\n    operation: \"Operation\",\n    enterOrderNo: \"Please enter order number\",\n    selectStatus: \"Please select status\",\n    pending: \"Pending Payment\",\n    paid: \"Paid\",\n    cancelled: \"Cancelled\",\n    refunded: \"Refunded\",\n    cancelOrder: \"Cancel Order\",\n    viewDetail: \"View Details\",\n    orderDetail: \"Order Details\",\n    fromLevel: \"From Level\",\n    toLevel: \"To Level\",\n    remark: \"Remark\",\n    noRemark: \"None\",\n    unpaid: \"Unpaid\",\n    confirmCancel: \"Are you sure to cancel this order?\",\n    cancelSuccess: \"Order cancelled successfully\",\n    cancelFailed: \"Failed to cancel order\",\n    getListFailed: \"Failed to get order list\",\n    balancePayment: \"Balance Payment\",\n    unknownLevel: \"Unknown Level\",\n    unknownStatus: \"Unknown Status\",\n    changeWarning: \"Please do not change frequently to avoid calculation confusion!\",\n    deductExperience: \"Deduct Experience\",\n    levelNames: {\n      1: \"Regular User\",\n      2: \"Silver User\",\n      3: \"Gold User\",\n      4: \"Diamond User\",\n      5: \"King User\",\n      6: \"Master User\"\n    }\n  },\n  grade: {\n    title: \"User Level\",\n    levelName: \"Level Name\",\n    experience: \"Experience\",\n    discount: \"Discount\",\n    commissionRate: \"Commission Rate\",\n    upgradeType: \"Upgrade Type\",\n    upgradeFee: \"Upgrade Fee\",\n    availableStatus: \"Available Status\",\n    status: \"Status\",\n    operation: \"Operation\",\n    available: \"Available\",\n    unavailable: \"Unavailable\",\n    free: \"Free\",\n    addUserLevel: \"Add User Level\",\n    levelIcon: \"Level Icon\",\n    enable: \"Enable\",\n    disable: \"Disable\",\n    edit: \"Edit\",\n    delete: \"Delete\",\n    deleteConfirm: \"Are you sure to delete? This will clear the corresponding user level data, please operate with caution!\",\n    deleteSuccess: \"Deleted successfully\",\n    updateSuccess: \"Updated successfully\",\n    hideConfirm: \"This operation will hide the corresponding user level, please operate with caution\",\n    userTypes: {\n      wechat: \"WeChat User\",\n      routine: \"Mini Program User\",\n      h5: \"H5 User\"\n    },\n    upgradeTypes: {\n      0: \"Register\",\n      1: \"Paid Purchase\",\n      2: \"Offline Application\",\n      3: \"Channel Partnership\"\n    },\n    form: {\n      dialogTitle: \"User Level\",\n      levelNameLabel: \"Level Name\",\n      levelNamePlaceholder: \"Please enter level name\",\n      gradeLabel: \"Grade\",\n      gradePlaceholder: \"Please enter grade\",\n      discountLabel: \"Discount (%)\",\n      discountPlaceholder: \"Please enter discount\",\n      experienceLabel: \"Experience\",\n      experiencePlaceholder: \"Please enter experience\",\n      iconLabel: \"Icon\",\n      cancel: \"Cancel\",\n      confirm: \"Confirm\",\n      editSuccess: \"Edit successful\",\n      addSuccess: \"Add successful\",\n      validation: {\n        levelNameRequired: \"Please enter level name\",\n        gradeRequired: \"Please enter grade\",\n        gradeNumber: \"Grade must be a number\",\n        discountRequired: \"Please enter discount\",\n        experienceRequired: \"Please enter experience\",\n        experienceNumber: \"Experience must be a number\",\n        iconRequired: \"Please upload icon\",\n        imageRequired: \"Please upload user background\"\n      }\n    }\n  }\n}), \"financial\", {\n  detail: _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    title: \"Financial Details\",\n    purchaseDetail: \"Membership Purchase Details\",\n    tradeDetail: \"Transaction Details\",\n    rechargeType: \"Product Name\",\n    transactionTime: \"Transaction Time\",\n    paymentMethod: \"Payment Method\",\n    electronicWallet: \"Electronic Wallet\",\n    bankName: \"Payment Bank\",\n    serialNumber: \"Serial Number\",\n    paymentTime: \"Payment Time\",\n    paymentNo: \"Payment No.\",\n    actualPaymentAmount: \"Actual Payment Amount\",\n    institutionNumber: \"Institution No.\",\n    paymentAccount: \"Payment Account\",\n    mobile: \"Phone Number\",\n    payee: \"Payee\",\n    payeeAccount: \"Payee Account\",\n    tradeNo: \"Transaction No.\",\n    tradeType: \"Transaction Type\",\n    tradeAmount: \"Transaction Amount (Rp)\",\n    userNickname: \"User Nickname\",\n    tikTokAccount: \"TikTok NickName\",\n    whatsApp: \"WhatsApp\",\n    channel: \"Channel\",\n    orderNo: \"Order No.\",\n    bankTransfer: \"Bank Transfer\"\n  }, \"electronicWallet\", \"Electronic Wallet\"), \"agentFee\", \"Agent Fee\"), \"partnerFee\", \"Partner Fee\"), \"exportExcel\", \"Export Excel\"), \"ShopeePay\", \"ShopeePay\"), \"DANA\", \"DANA\"), \"OVO\", \"OVO\"), \"Gopay\", \"Gopay\"),\n  request: (_request = {\n    walletWithdrawal: \"Wallet Withdrawal\",\n    bankWithdrawal: \"Bank Withdrawal\",\n    applicant: \"Applicant\",\n    applicationTime: \"Application Time\",\n    electronicWallet: \"Electronic Wallet\",\n    bankName: \"Bank Name\",\n    serialNumber: \"Serial Number\",\n    applicationId: \"Application ID\",\n    applicantName: \"Applicant\",\n    withdrawalAmount: \"Withdrawal Amount\",\n    serviceFee: \"Service Fee\",\n    actualAmount: \"Actual Amount\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_request, \"applicationTime\", \"Application Time\"), \"walletCode\", \"Electronic Wallet\"), \"walletAccount\", \"Account\"), \"bankCardNumber\", \"Bank Card Number\"), \"name\", \"Name\"), \"phoneNumber\", \"Phone Number\"), \"action\", \"Action\"), \"transferComplete\", \"Transfer Complete\"), \"attachment\", \"Attachment\"), \"remark\", \"Remark\"), _defineProperty(_defineProperty(_defineProperty(_request, \"confirm\", \"Confirm\"), \"cancel\", \"Cancel\"), \"exportExcel\", \"Export Excel\")),\n  history: (_history = {\n    walletWithdrawal: \"Wallet Withdrawal\",\n    bankWithdrawal: \"Bank Withdrawal\",\n    applicant: \"Applicant\",\n    applicationTime: \"Application Time\",\n    electronicWallet: \"Electronic Wallet\",\n    bankName: \"Bank Name\",\n    serialNumber: \"Serial Number\",\n    applicationId: \"Application ID\",\n    applicantName: \"Applicant\",\n    withdrawalAmount: \"Withdrawal Amount\",\n    serviceFee: \"Service Fee\",\n    actualAmount: \"Actual Amount\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_history, \"applicationTime\", \"Application Time\"), \"walletCode\", \"Electronic Wallet\"), \"walletAccount\", \"Account\"), \"bankCardNumber\", \"Bank Card Number\"), \"name\", \"Name\"), \"phoneNumber\", \"Phone Number\"), \"transferTime\", \"Transfer Time\"), \"transferResult\", \"Transfer Result\"), \"remark\", \"Remark\"), \"attachment\", \"Attachment\"), _defineProperty(_defineProperty(_defineProperty(_history, \"operator\", \"Operator\"), \"exportExcel\", \"Export Excel\"), \"status\", \"Withdrawal Status\"))\n}), \"parameter\", {\n  rewardRules: {\n    title: \"Reward Rules Setup\",\n    rewardTemplateName: \"Template Name\",\n    rewardTemplateId: \"Reward Rule Template ID\",\n    directInviteReward: \"Direct Invite Reward per Person\",\n    secondLevelInviteReward: \"Second Level Invite Reward per Person (Rp)\",\n    thirdLevelInviteReward: \"Third Level Invite Reward per Person (Rp)\",\n    goldRewardPer10: \"Gold Reward per 10 Invites (Rp)\",\n    diamondRewardPer10: \"Diamond Reward per 10 Invites (Rp)\",\n    operation: \"Operation\",\n    edit: \"Edit\",\n    editTitle: \"Edit Reward Rule Settings\",\n    directAgentLabel: \"Direct invitee is agent\",\n    directPartnerLabel: \"Direct invitee is partner\",\n    indirectAgent2LevelLabel: \"Indirect invite (Level 2 is agent) reward per person (Rp)\",\n    indirectPartner2LevelLabel: \"Indirect invite (Level 2 is partner) reward per person (Rp)\",\n    indirectAgent3LevelLabel: \"Indirect invite (Level 3 is agent) reward per person (Rp)\",\n    indirectPartner3LevelLabel: \"Indirect invite (Level 3 is partner) reward per person (Rp)\"\n  },\n  withdrawalFee: {\n    title: \"Withdrawal Fee Setup\",\n    feeTemplateId: \"Fee Template ID\",\n    minWithdrawAmount: \"Minimum Withdrawal Amount (Rp)\",\n    maxWithdrawAmount: \"Maximum Withdrawal Amount (Rp)\",\n    withdrawFeeRate: \"Withdrawal Fee Rate (%)\",\n    operation: \"Operation\",\n    edit: \"Edit\",\n    addTitle: \"Add Fee Rule\",\n    editTitle: \"Edit Fee Rule\",\n    placeholder: {\n      couponId: \"Please enter coupon ID\",\n      minWithdrawAmount: \"Please enter minimum withdrawal amount\",\n      maxWithdrawAmount: \"Please enter maximum withdrawal amount\",\n      withdrawFeeRate: \"Please enter withdrawal fee rate\"\n    }\n  },\n  membershipFee: {\n    title: \"Membership Upgrade Fee Setup\",\n    feeTemplateId: \"Fee Template ID\",\n    agentFee: \"Agent Fee (Rp)\",\n    partnerFee: \"Partner Fee (Rp)\",\n    operation: \"Operation\",\n    edit: \"Edit\",\n    addTitle: \"Add Membership Upgrade Fee\",\n    editTitle: \"Edit Membership Upgrade Fee\",\n    placeholder: {\n      agentFee: \"Please enter agent fee\",\n      partnerFee: \"Please enter partner fee\"\n    }\n  },\n  shoppingCashbackRules: {\n    directCashbackRate: \"Direct Cashback Rate (%)\",\n    secondLevelCashbackRate: \"Second Level Cashback Rate (%)\",\n    thirdLevelCashbackRate: \"Third Level Cashback Rate (%)\",\n    normalUserRule: \"Normal User Cashback Rule\",\n    agentTeamRule: \"Agent Team Cashback Rule\",\n    partnerTeamRule: \"Partner Team Cashback Rule\"\n  },\n  referralRewardConfig: {\n    title: \"Referral Reward Configuration\",\n    rewardTemplateId: \"Reward Rule Template ID\",\n    rewardTemplateName: \"Template Name\",\n    referralCount: \"Referral Count\",\n    firstOrderCount: \"First Order Count\",\n    rewardAmount: \"Reward Amount (Rp)\",\n    rewardRuleZh: \"Reward Rule (Chinese)\",\n    rewardRuleEn: \"Reward Rule (English)\",\n    rewardRuleId: \"Reward Rule (Indonesian)\",\n    operation: \"Operation\",\n    edit: \"Edit\",\n    editTitle: \"Edit Referral Reward Configuration\",\n    basicConfig: \"Basic Configuration\",\n    validation: {\n      referralCountMin: \"Referral count must be greater than or equal to 0\",\n      firstOrderCountMin: \"First order count must be greater than or equal to 0\",\n      firstOrderCountMax: \"First order count must be less than referral count\",\n      rewardAmountMin: \"Reward amount must be greater than or equal to 0\"\n    }\n  }\n}), \"admin\", {\n  system: {\n    role: {\n      roleName: \"Role Name\",\n      roleId: \"Role ID\",\n      status: \"Status\",\n      createTime: \"Create Time\",\n      updateTime: \"Update Time\",\n      operation: \"Operation\",\n      addRole: \"Add Role\",\n      editRole: \"Edit Role\",\n      deleteRole: \"Delete Role\",\n      confirmDelete: \"Are you sure to delete current data?\",\n      deleteSuccess: \"Role deleted successfully\",\n      createIdentity: \"Create Identity\",\n      editIdentity: \"Edit Identity\",\n      roleForm: {\n        roleNameLabel: \"Role Name\",\n        roleNamePlaceholder: \"Identity Name\",\n        statusLabel: \"Status\",\n        menuPermissions: \"Menu Permissions\",\n        expandCollapse: \"Expand/Collapse\",\n        selectAll: \"Select All/None\",\n        parentChildLink: \"Parent-Child Link\",\n        confirm: \"Confirm\",\n        update: \"Update\",\n        cancel: \"Cancel\"\n      }\n    },\n    admin: (_admin = {\n      role: \"Role\",\n      status: \"Status\",\n      realName: \"Name or Account\",\n      id: \"ID\",\n      account: \"Account\",\n      phone: \"Phone\",\n      lastTime: \"Last Login Time\",\n      lastIp: \"Last Login IP\",\n      isSms: \"Receive SMS\",\n      isDel: \"Delete Flag\",\n      operation: \"Operation\",\n      addAdmin: \"Add Admin\",\n      edit: \"Edit\",\n      delete: \"Delete\",\n      createIdentity: \"Create Identity\",\n      editIdentity: \"Edit Identity\",\n      pleaseAddPhone: \"Please add a phone number for the admin first!\",\n      confirmDelete: \"Are you sure to delete current data?\",\n      deleteSuccess: \"Data deleted successfully\"\n    }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"account\", \"Account\"), \"pwd\", \"Password\"), \"repwd\", \"Confirm Password\"), \"realName\", \"Real Name\"), \"roles\", \"Roles\"), \"phone\", \"Phone\"), \"pleaseAddPhone\", \"Please add a phone number for the admin first!\"), \"validatePhone\", {\n      required: \"Please enter phone number\",\n      formatError: \"Phone number format is incorrect!\"\n    }), \"validatePass\", {\n      required: \"Please re-enter password\",\n      notMatch: \"Passwords do not match!\"\n    }), \"message\", {\n      createSuccess: \"Admin created successfully\",\n      updateSuccess: \"Admin updated successfully\"\n    }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"validateAccount\", {\n      required: \"Please enter admin account\"\n    }), \"validatePassword\", {\n      required: \"Please enter admin password\"\n    }), \"validateConfirmPassword\", {\n      required: \"Please confirm password\"\n    }), \"validateRealName\", {\n      required: \"Please enter admin name\"\n    }), \"validateRoles\", {\n      required: \"Please select admin role\"\n    }), \"validatePassword\", {\n      required: \"Please enter admin password\",\n      lengthError: \"Password length should be 6-20 characters\"\n    }), \"validateConfirmPassword\", {\n      required: \"Please confirm password\"\n    }), \"validatePass\", {\n      notMatch: \"Passwords do not match\"\n    }))\n  }\n}), \"permissionRules\", {\n  menuName: \"Menu Name\",\n  status: \"Status\",\n  select: \"Please Select\",\n  add: \"Add\",\n  expandCollapse: \"Expand/Collapse\",\n  actions: {\n    edit: \"Edit\",\n    add: \"Add\",\n    delete: \"Delete\"\n  },\n  table: {\n    menuName: \"Menu Name\",\n    icon: \"Icon\",\n    sort: \"Sort\",\n    perm: \"Permission\",\n    component: \"Component Path\",\n    status: \"Status\",\n    createTime: \"Create Time\",\n    type: \"Type\"\n  },\n  menuType: {\n    directory: \"Directory\",\n    menu: \"Menu\",\n    button: \"Button\"\n  },\n  form: {\n    parentMenu: \"Parent Menu\",\n    menuType: \"Menu Type\",\n    menuIcon: \"Menu Icon\",\n    menuName: \"Menu Name\",\n    sort: \"Display Sort\",\n    component: \"Component Path\",\n    componentTip: \"Component path, e.g. `system/user/index`, default in `views` folder\",\n    perm: \"Permission Character\",\n    permTip: 'Permission character defined in controller, e.g. @PreAuthorize(`@ss.hasPermi(\"system: user: list\")`)',\n    showStatus: \"Display Status\",\n    showStatusTip: \"If hidden, the route will not appear in the sidebar but can still be accessed\",\n    enterMenuName: \"Please enter menu name\",\n    enterComponent: \"Please enter component path\",\n    enterPerm: \"Please enter permission character\",\n    selectIcon: \"Please select menu icon\",\n    selectParentMenu: \"Select parent menu\",\n    sortRequired: \"Display sort cannot be empty\"\n  }\n}), \"affiliateProducts\", (_affiliateProducts = {\n  title: \"Affiliate Products\",\n  keywords: \"Keywords:\",\n  keywordsPlaceholder: \"Enter product keywords\",\n  priceRange: \"Price Range:\",\n  minPrice: \"Min Price\",\n  maxPrice: \"Max Price\",\n  commissionRange: \"Commission Rate Range:\",\n  minCommission: \"Min Commission Rate (%)\",\n  maxCommission: \"Max Commission Rate (%)\",\n  sort: \"Sort:\",\n  sortCommissionRate: \"Commission Rate\",\n  sortCommission: \"Commission Amount\",\n  sortPrice: \"Product Price\",\n  sortSales: \"Sales\",\n  sortDesc: \"Descending\",\n  sortAsc: \"Ascending\",\n  query: \"Query\",\n  reset: \"Reset\",\n  refresh: \"Refresh\",\n  listTitle: \"Affiliate Products List\",\n  batchImport: \"Batch Import\",\n  batchImporting: \"Batch Importing...\",\n  batchDelete: \"Batch Delete\",\n  batchDeleting: \"Batch Deleting...\",\n  emptyTip: \"Click the query button to start searching for products\",\n  serialNumber: \"Serial Number\",\n  productImage: \"Product Image\",\n  productTitle: \"Product Title\",\n  shop: \"Shop\",\n  originalPrice: \"Original Price\",\n  salesPrice: \"Sales Price\",\n  commissionRate: \"Commission Rate\",\n  commissionAmount: \"Commission Amount\",\n  unitsSold: \"Units Sold\",\n  inventoryStatus: \"Inventory Status\",\n  hasInventory: \"In Stock\",\n  noInventory: \"Out of Stock\",\n  saleRegion: \"Sale Region\",\n  importStatus: \"Import Status\",\n  imported: \"Imported\",\n  notImported: \"Not Imported\",\n  action: \"Action\",\n  import: \"Import Product\",\n  importing: \"Importing...\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"imported\", \"Imported\"), \"delete\", \"Delete\"), \"deleting\", \"Deleting...\"), \"prevPage\", \"Previous Page\"), \"nextPage\", \"Next Page\"), \"pageSize\", \"Items per page:\"), \"totalCount\", \"Total {count} products\"), \"importSingle\", \"Import Single Product\"), \"importBatch\", \"Batch Import Products\"), \"selectedCount\", \"Selected Products:\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"brandAutoDetect\", \"Brand information will be automatically identified from TikTok product data, no manual selection required\"), \"confirmImport\", \"Confirm Import\"), \"cancel\", \"Cancel\"), \"deleteConfirm\", \"Are you sure you want to delete this product?\"), \"batchDeleteConfirm\", \"Are you sure you want to batch delete {count} products?\"), \"deleteSuccess\", \"Delete successful\"), \"batchDeleteSuccess\", \"Batch delete successful\"), \"importSuccess\", \"Product imported successfully!\"), \"batchImportSuccess\", \"Batch import completed! Successfully imported {count} products\"), \"importExists\", \"Product already exists, no need to import again\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"batchImportExists\", \"All products already exist, no need to import again\"), \"batchImportPartial\", \"Batch import partially successful! Success: {success}, Failed: {failed}, Skipped: {skipped}\"), \"batchImportMixed\", \"Batch import completed! Success: {success}, Skipped (already exists): {skipped}\"), \"importFailed\", \"Product import failed! Reason: {reason}\"), \"batchImportFailed\", \"Batch import failed! Failed: {failed}, Skipped: {skipped}\"), \"selectFirst\", \"Please select products to import first\"), \"selectDeleteFirst\", \"Please select products to delete first\"), \"searchFirst\", \"Please click the query button first\"), \"noResults\", \"No products found matching the criteria\"), \"commissionRangeError\", \"Commission rate must be ≥ 1000 or equal to 0\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"commissionInvalidNumber\", \"Please enter a valid number\"), \"pleaseFixErrors\", \"Please fix input errors before searching\"), \"addKeywordPlaceholder\", \"Enter keyword and press Enter to add\"), \"keywordTooLong\", \"Keyword length cannot exceed 255 characters\"), \"keywordDuplicate\", \"Keyword already exists, please do not add duplicates\"), \"keywordLimitExceeded\", \"Number of keywords cannot exceed 20\"), \"selectedKeywords\", \"Selected Keywords:\"), \"clearAll\", \"Clear All\"), \"clearAllKeywords\", \"Clear All Keywords\"), \"confirmClearAllKeywords\", \"Are you sure you want to clear all selected keywords?\"), _defineProperty(_affiliateProducts, \"keywordsClearedSuccess\", \"All keywords have been cleared\"))), \"chainTransferRecord\", {\n  title: \"Chain Transfer Record\",\n  keyword: \"Keyword\",\n  brandName: \"Brand Name\",\n  query: \"Query\",\n  reset: \"Reset\",\n  exportExcel: \"Export Excel\",\n  serialNumber: \"Serial Number\",\n  nickname: \"Nickname\",\n  tiktokId: \"TikTok ID\",\n  originalLink: \"Original Link\",\n  rebateLink: \"Rebated Link After Chain Transfer\",\n  operationTime: \"Operation Time\",\n  linkSource: \"Link Source\",\n  productId: \"Product ID\",\n  productName: \"Product Name\",\n  productPrice: \"Product Price\",\n  productCashbackRate: \"Product Cashback Rate\",\n  userCashbackRate: \"User Cashback Rate\",\n  enterProductName: \"Please enter product name\"\n}), \"message\", {\n  hello: \"Hello\",\n  userNotice: \"User Notice\",\n  userDetails: {\n    balance: \"Balance\",\n    allOrderCount: \"Total Orders\",\n    allConsumeCount: \"Total Consumption\",\n    integralCount: \"Points\",\n    mothOrderCount: \"Orders This Month\",\n    mothConsumeCount: \"Consumption This Month\",\n    consumeRecord: \"Consumption Record\",\n    integralDetail: \"Points Detail\",\n    signInRecord: \"Sign-in Record\",\n    coupons: \"Owned Coupons\",\n    balanceChange: \"Balance Change\",\n    friendRelation: \"Friend Relation\",\n    sourceOrPurpose: \"Source/Purpose\",\n    integralChange: \"Points Change\",\n    balanceAfterChange: \"Points After Change\",\n    date: \"Date\",\n    remark: \"Remark\",\n    orderId: \"Order ID\",\n    receiver: \"Receiver\",\n    goodsNum: \"Goods Quantity\",\n    goodsTotalPrice: \"Goods Total Price\",\n    payPrice: \"Paid Amount\",\n    payTime: \"Transaction Time\",\n    action: \"Action\",\n    getIntegral: \"Points Earned\",\n    signTime: \"Sign-in Time\",\n    couponName: \"Coupon Name\",\n    faceValue: \"Face Value\",\n    validity: \"Validity\",\n    minPrice: \"Min Consumption\",\n    exchangeTime: \"Exchange Time\",\n    changeAmount: \"Change Amount\",\n    afterChange: \"After Change\",\n    type: \"Type\",\n    createTime: \"Create Time\",\n    id: \"ID\",\n    nickname: \"Nickname\",\n    level: \"Level\",\n    joinTime: \"Join Time\"\n  }\n}));", null]}