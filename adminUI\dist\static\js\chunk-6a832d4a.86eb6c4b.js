(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a832d4a"],{"3e45":function(t,e,a){"use strict";a("6c03")},"6c03":function(t,e,a){},e89b:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"是否显示："}},[a("el-select",{staticClass:"filter-item selWidth",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[a("el-option",{attrs:{label:"关闭",value:0}}),t._v(" "),a("el-option",{attrs:{label:"开启",value:1}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"配置名称："}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.timeId,callback:function(e){t.$set(t.tableFrom,"timeId",e)},expression:"tableFrom.timeId"}},t._l(t.seckillTime,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name+" - "+t.time,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品ID/名称",clearable:""},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1),t._v(" "),a("router-link",{attrs:{to:{path:"/marketing/seckill/creatSeckill/creat"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:seckill:save"],expression:"['admin:seckill:save']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"}},[t._v("添加秒杀商品")])],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"配置","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v(t._s(e.row.storeSeckillManagerResponse?e.row.storeSeckillManagerResponse.name:"-"))]),t._v(" "),a("div",[t._v(t._s(e.row.startTime+" - "+e.row.stopTime))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"秒杀时段","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v(t._s(e.row.storeSeckillManagerResponse?e.row.storeSeckillManagerResponse.time.split(",").join(" - "):"-"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品标题",prop:"title","min-width":"300","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{label:"活动简介","min-width":"300",prop:"info","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{label:"原价",prop:"otPrice","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"秒杀价","min-width":"100",prop:"price"}}),t._v(" "),a("el-table-column",{attrs:{label:"限量",prop:"quotaShow","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{label:"限量剩余","min-width":"80",prop:"quota"}}),t._v(" "),a("el-table-column",{attrs:{label:"秒杀状态","min-width":"100",prop:"statusName"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["admin:seckill:update:status"])?[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},on:{change:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{attrs:{to:{path:"/marketing/seckill/creatSeckill/updeta/"+e.row.productId+"/"+e.row.id}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:seckill:info"],expression:"['admin:seckill:info']"}],attrs:{type:"text",size:"small"}},[t._v("编辑")])],1),t._v(" "),2!==e.row.killStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:seckill:delete"],expression:"['admin:seckill:delete']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")]):t._e()]}}])})],1),t._v(" "),a("div",{staticClass:"block mb20"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)},n=[],r=a("b7be"),l=a("02df"),o=a("e350");function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",n=a.toStringTag||"@@toStringTag";function r(a,i,n,r){var s=i&&i.prototype instanceof o?i:o,u=Object.create(s.prototype);return c(u,"_invoke",function(a,i,n){var r,o,s,c=0,u=n||[],m=!1,d={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,a){return r=e,o=0,s=t,d.n=a,l}};function p(a,i){for(o=a,s=i,e=0;!m&&c&&!n&&e<u.length;e++){var n,r=u[e],p=d.p,h=r[2];a>3?(n=h===i)&&(s=r[(o=r[4])?5:(o=3,3)],r[4]=r[5]=t):r[0]<=p&&((n=a<2&&p<r[1])?(o=0,d.v=i,d.n=r[1]):p<h&&(n=a<3||r[0]>i||i>h)&&(r[4]=a,r[5]=i,d.n=h,o=0))}if(n||a>1)return l;throw m=!0,i}return function(n,u,h){if(c>1)throw TypeError("Generator is already running");for(m&&1===u&&p(u,h),o=u,s=h;(e=o<2?t:s)||!m;){r||(o?o<3?(o>1&&(d.n=-1),p(o,s)):d.n=s:d.v=s);try{if(c=2,r){if(o||(n="next"),e=r[n]){if(!(e=e.call(r,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,o<2&&(o=0)}else 1===o&&(e=r.return)&&e.call(r),o<2&&(s=TypeError("The iterator does not provide a '"+n+"' method"),o=1);r=t}else if((e=(m=d.n<0)?s:a.call(i,d))!==l)break}catch(e){r=t,o=1,s=e}finally{c=1}}return{value:e,done:m}}}(a,n,r),!0),u}var l={};function o(){}function u(){}function m(){}e=Object.getPrototypeOf;var d=[][i]?e(e([][i]())):(c(e={},i,(function(){return this})),e),p=m.prototype=o.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,c(t,n,"GeneratorFunction")),t.prototype=Object.create(p),t}return u.prototype=m,c(p,"constructor",m),c(m,"constructor",u),u.displayName="GeneratorFunction",c(m,n,"GeneratorFunction"),c(p),c(p,n,"Generator"),c(p,i,(function(){return this})),c(p,"toString",(function(){return"[object Generator]"})),(s=function(){return{w:r,m:h}})()}function c(t,e,a,i){var n=Object.defineProperty;try{n({},"",{})}catch(t){n=0}c=function(t,e,a,i){function r(e,a){c(t,e,(function(t){return this._invoke(e,a,t)}))}e?n?n(t,e,{value:a,enumerable:!i,configurable:!i,writable:!i}):t[e]=a:(r("next",0),r("throw",1),r("return",2))},c(t,e,a,i)}function u(t,e,a,i,n,r,l){try{var o=t[r](l),s=o.value}catch(t){return void a(t)}o.done?e(s):Promise.resolve(s).then(i,n)}function m(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var r=t.apply(e,a);function l(t){u(r,i,n,l,o,"next",t)}function o(t){u(r,i,n,l,o,"throw",t)}l(void 0)}))}}var d={name:"SeckillList",data:function(){return{listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,timeId:"",status:"",keywords:""},seckillTime:[]}},mounted:function(){var t=this;Object(l["a"])().then((function(e){t.seckillTime=e.list})),this.tableFrom.timeId=Number(this.$route.params.timeId)||"",this.getList()},methods:{checkPermi:o["a"],handleDelete:function(t,e){var a=this;this.$modalSure().then((function(){Object(r["H"])({id:t}).then((function(){a.$message.success("删除成功"),a.tableData.data.splice(e,1)}))}))},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(r["J"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},onchangeIsShow:function(t){var e=this;Object(r["L"])({id:t.id,status:t.status}).then(m(s().m((function t(){return s().w((function(t){while(1)switch(t.n){case 0:e.$message.success("修改成功"),e.getList();case 1:return t.a(2)}}),t)})))).catch((function(){t.status=!t.status}))}}},p=d,h=(a("3e45"),a("2877")),f=Object(h["a"])(p,i,n,!1,null,"373ef138",null);e["default"]=f.exports}}]);