(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-639b006c"],{"2f2c":function(e,t,r){"use strict";r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return d})),r.d(t,"r",(function(){return m})),r.d(t,"d",(function(){return p})),r.d(t,"a",(function(){return f})),r.d(t,"g",(function(){return h})),r.d(t,"h",(function(){return b})),r.d(t,"j",(function(){return v})),r.d(t,"i",(function(){return g})),r.d(t,"e",(function(){return y})),r.d(t,"o",(function(){return w})),r.d(t,"q",(function(){return F})),r.d(t,"l",(function(){return O})),r.d(t,"m",(function(){return x})),r.d(t,"n",(function(){return j})),r.d(t,"p",(function(){return _})),r.d(t,"k",(function(){return k})),r.d(t,"f",(function(){return C}));var n=r("b775");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function s(e,t,r){return(t=l(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e){var t=c(e,"string");return"symbol"==a(t)?t:t+""}function c(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(e){return Object(n["a"])({url:"/admin/system/city/list",method:"get",params:o({},e)})}function d(){return Object(n["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(e){return Object(n["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},e)})}function p(e){return Object(n["a"])({url:"/admin/system/city/update",method:"post",params:o({},e)})}function f(e){return Object(n["a"])({url:"/admin/system/city/info",method:"get",params:o({},e)})}function h(e){return Object(n["a"])({url:"/admin/express/list",method:"get",params:o({},e)})}function b(){return Object(n["a"])({url:"/admin/express/sync/express",method:"post"})}function v(e){return Object(n["a"])({url:"/admin/express/update/show",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/admin/express/update",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/admin/express/delete",method:"GET",params:o({},e)})}function w(e){return Object(n["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},e)})}function F(e){return Object(n["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},e)})}function O(e){return Object(n["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},e)})}function x(e){return Object(n["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},e)})}function j(e){return Object(n["a"])({url:"admin/express/shipping/templates/save",method:"post",data:e})}function _(e,t){return Object(n["a"])({url:"admin/express/shipping/templates/update",method:"post",data:e,params:o({},t)})}function k(e){return Object(n["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:e})}function C(e){return Object(n["a"])({url:"admin/express/info",method:"get",params:o({},e)})}},"36d0":function(e,t,r){"use strict";r("f8ef")},5317:function(e,t,r){"use strict";var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div")},a=[],i=r("2877"),o={},s=Object(i["a"])(o,n,a,!1,null,null,null);t["a"]=s.exports},"9e95":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e.checkPermi(["admin:system:store:count","admin:system:store:list"])?r("el-tabs",{on:{"tab-click":e.onClickTab},model:{value:e.artFrom.status,callback:function(t){e.$set(e.artFrom,"status",t)},expression:"artFrom.status"}},[r("el-tab-pane",{attrs:{label:"显示中的提货点("+e.headerCount.show+")",name:"1"}}),e._v(" "),r("el-tab-pane",{attrs:{label:"隐藏中的提货点("+e.headerCount.hide+")",name:"0"}}),e._v(" "),r("el-tab-pane",{attrs:{label:"回收站的提货点("+e.headerCount.recycle+")",name:"2"}})],1):e._e(),e._v(" "),r("el-form",{ref:"form",attrs:{inline:"",model:e.artFrom},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{label:"关键字："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入提货点名称/电话",size:"small",clearable:""},model:{value:e.artFrom.keywords,callback:function(t){e.$set(e.artFrom,"keywords",t)},expression:"artFrom.keywords"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1)],1),e._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:save"],expression:"['admin:system:store:save']"}],attrs:{type:"primary",size:"small"},on:{click:e.add}},[e._v("添加提货点")])],1),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{size:"small",data:e.tableData,"header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"80"}}),e._v(" "),r("el-table-column",{attrs:{prop:"image",label:"提货点图片","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;e.index;return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.image,"preview-src-list":[t.image]}})],1)]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"name",label:"提货点名称","min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{prop:"phone",label:"提货点电话","min-width":"100"}}),e._v(" "),r("el-table-column",{attrs:{prop:"detailedAddress",label:"地址","min-width":"100"}}),e._v(" "),r("el-table-column",{attrs:{prop:"dayTime",label:"营业时间","min-width":"180"}}),e._v(" "),r("el-table-column",{attrs:{prop:"isShow",label:"是否显示","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;t.index;return e.checkPermi(["admin:system:store:update:status"])?[r("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(t){return e.onchangeIsShow(n.id,n.isShow)}},model:{value:n.isShow,callback:function(t){e.$set(n,"isShow",t)},expression:"row.isShow"}})]:void 0}}],null,!0)}),e._v(" "),r("el-table-column",{attrs:{fixed:"right",label:"操作","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;t.index;return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:info"],expression:"['admin:system:store:info']"}],attrs:{type:"text",size:"small"},on:{click:function(t){return e.edit(n.id)}}},[e._v("编辑")]),e._v(" "),r("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),"2"===e.artFrom.status?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:recovery"],expression:"['admin:system:store:recovery']"}],attrs:{type:"text",size:"small"},on:{click:function(t){return e.storeRecovery(n.id)}}},[e._v("恢复")]):e._e(),e._v(" "),"2"===e.artFrom.status?r("el-divider",{attrs:{direction:"vertical"}}):e._e(),e._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:delete","admin:system:store:completely:delete"],expression:"['admin:system:store:delete','admin:system:store:completely:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(t){"2"===e.artFrom.status?e.allDelete(n.id):e.storeDelete(n.id)}}},[e._v("删除")])]}}])})],1),e._v(" "),r("el-pagination",{staticClass:"mt20",attrs:{"current-page":e.artFrom.page,"page-sizes":[20,40,60,100],"page-size":e.artFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.sizeChange,"current-change":e.pageChange}})],1),e._v(" "),r("system-store",{ref:"template"})],1)},a=[],i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.id?"修改提货点":"添加提货点",visible:e.dialogFormVisible,width:"750px"},on:{"update:visible":function(t){e.dialogFormVisible=t},close:e.cancel},model:{value:e.dialogFormVisible,callback:function(t){e.dialogFormVisible=t},expression:"dialogFormVisible"}},[e.dialogFormVisible?[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"150px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{label:"提货点名称：",prop:"name"}},[r("el-input",{staticClass:"dialogWidth",attrs:{maxlength:"40",placeholder:"请输入提货点名称"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提货点简介："}},[r("el-input",{staticClass:"dialogWidth",attrs:{maxlength:"100",placeholder:"请输入提货点简介"},model:{value:e.ruleForm.introduction,callback:function(t){e.$set(e.ruleForm,"introduction",t)},expression:"ruleForm.introduction"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提货点手机号：",prop:"phone"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入提货点手机号"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提货点地址：",prop:"address"}},[r("el-cascader",{staticClass:"dialogWidth",attrs:{clearable:"",options:e.addresData,props:{value:"name",label:"name",children:"child",expandTrigger:"hover"}},on:{change:e.handleChange},model:{value:e.ruleForm.address,callback:function(t){e.$set(e.ruleForm,"address",t)},expression:"ruleForm.address"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"详细地址：",prop:"detailedAddress"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入详细地址"},model:{value:e.ruleForm.detailedAddress,callback:function(t){e.$set(e.ruleForm,"detailedAddress",t)},expression:"ruleForm.detailedAddress"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提货点营业："}},[r("el-time-picker",{attrs:{"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"请选择时间营业时间","value-format":"HH:mm:ss"},on:{change:e.onchangeTime},model:{value:e.dayTime,callback:function(t){e.dayTime=t},expression:"dayTime"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"提货点logo："}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1")}}},[e.ruleForm.image?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:e.ruleForm.image}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),e._v(" "),r("el-form-item",{attrs:{label:"经纬度：",prop:"latitude"}},[r("el-tooltip",{attrs:{content:"请点击查找位置选择位置"}},[r("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请查找位置",readOnly:""},model:{value:e.ruleForm.latitude,callback:function(t){e.$set(e.ruleForm,"latitude",t)},expression:"ruleForm.latitude"}},[r("el-button",{attrs:{slot:"append"},on:{click:e.onSearch},slot:"append"},[e._v("查找位置")])],1)],1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.cancel}},[e._v("取 消")]),e._v(" "),e.id?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:update"],expression:"['admin:system:store:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.editForm("ruleForm")}}},[e._v("修改")]):r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:store:save"],expression:"['admin:system:store:save']"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("提交")])],1),e._v(" "),r("el-dialog",{staticClass:"mapBox",attrs:{title:"上传经纬度",visible:e.modalMap,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.modalMap=t}},model:{value:e.modalMap,callback:function(t){e.modalMap=t},expression:"modalMap"}},[r("iframe",{attrs:{id:"mapPage",width:"100%",height:"100%",frameborder:"0",src:e.keyUrl}})])]:e._e()],2)},o=[],s=r("6537"),l=r("2f2c"),c=r("2b9b"),u=r("5317"),d=r("61f7");function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function i(r,n,a,i){var l=n&&n.prototype instanceof s?n:s,c=Object.create(l.prototype);return p(c,"_invoke",function(r,n,a){var i,s,l,c=0,u=a||[],d=!1,m={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,r){return i=t,s=0,l=e,m.n=r,o}};function p(r,n){for(s=r,l=n,t=0;!d&&c&&!a&&t<u.length;t++){var a,i=u[t],p=m.p,f=i[2];r>3?(a=f===n)&&(l=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((a=r<2&&p<i[1])?(s=0,m.v=n,m.n=i[1]):p<f&&(a=r<3||i[0]>n||n>f)&&(i[4]=r,i[5]=n,m.n=f,s=0))}if(a||r>1)return o;throw d=!0,n}return function(a,u,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&p(u,f),s=u,l=f;(t=s<2?e:l)||!d;){i||(s?s<3?(s>1&&(m.n=-1),p(s,l)):m.n=l:m.v=l);try{if(c=2,i){if(s||(a="next"),t=i[a]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),s=1);i=e}else if((t=(d=m.n<0)?l:r.call(n,m))!==o)break}catch(t){i=e,s=1,l=t}finally{c=1}}return{value:t,done:d}}}(r,a,i),!0),c}var o={};function s(){}function l(){}function c(){}t=Object.getPrototypeOf;var u=[][n]?t(t([][n]())):(p(t={},n,(function(){return this})),t),d=c.prototype=s.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,p(e,a,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=c,p(d,"constructor",c),p(c,"constructor",l),l.displayName="GeneratorFunction",p(c,a,"GeneratorFunction"),p(d),p(d,a,"Generator"),p(d,n,(function(){return this})),p(d,"toString",(function(){return"[object Generator]"})),(m=function(){return{w:i,m:f}})()}function p(e,t,r,n){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}p=function(e,t,r,n){function i(t,r){p(e,t,(function(e){return this._invoke(t,r,e)}))}t?a?a(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r:(i("next",0),i("throw",1),i("return",2))},p(e,t,r,n)}function f(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function h(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){f(i,n,a,o,s,"next",e)}function s(e){f(i,n,a,o,s,"throw",e)}o(void 0)}))}}var b={name:"index",components:{Templates:u["a"]},data:function(){var e=this,t=function(e,t,r){if(!t)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(t)?r():r(new Error("手机号格式不正确!"))},r=function(t,r,n){e.ruleForm.image?n():n(new Error("请上传提货点logo"))};return{loading:!1,dialogFormVisible:!1,modalMap:!1,keyUrl:"",addresData:[],ruleForm:{name:"",introduction:"",phone:"",address:"",detailedAddress:"",dayTime:"",image:"",latitude:""},id:0,dayTime:["",""],rules:{name:[{required:!0,message:"请输入提货点名称",trigger:"blur"}],address:[{required:!0,message:"请选择提货点地址",trigger:"change"}],dayTime:[{required:!0,type:"array",message:"请选择提货点营业时间",trigger:"change"}],phone:[{required:!0,validator:t,trigger:"blur"}],detailedAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"}],image:[{required:!0,validator:r,trigger:"change"}],latitude:[{required:!0,message:"请选择经纬度",trigger:"blur"}]}}},created:function(){this.ruleForm.image="";var e=JSON.parse(sessionStorage.getItem("cityList"));this.addresData=e,this.getCityList(),this.getKey()},mounted:function(){window.addEventListener("message",(function(e){var t=e.data;t&&"locationPicker"===t.module&&window.parent.selectAdderss(t)}),!1),window.selectAdderss=this.selectAdderss},methods:{getInfo:function(e){var t=this,r=this;r.id=e,this.loading=!0,Object(s["e"])({id:e}).then((function(e){r.ruleForm=e,r.ruleForm.address=e.address.split(","),r.dayTime=e.dayTime.split(","),t.loading=!1}))},cancel:function(){this.dialogFormVisible=!1,this.clearFrom(),this.ruleForm.image="",this.resetForm("ruleForm"),this.id=0},resetForm:function(e){this.$refs[e].resetFields()},submitForm:Object(d["a"])((function(e){var t=this;this.$refs[e].validate((function(r){if(!r)return!1;Object(s["h"])(t.ruleForm).then(h(m().m((function r(){return m().w((function(r){while(1)switch(r.n){case 0:t.$message.success("提交成功"),t.dialogFormVisible=!1,t.$parent.tableList(),t.$parent.storeGetCount(),t.clearFrom(),t.resetForm(e),t.id=0;case 1:return r.a(2)}}),r)}))))}))})),editForm:Object(d["a"])((function(e){var t=this;this.$refs[e].validate((function(r){if(!r)return!1;t.handleChange(t.ruleForm.address),Object(s["o"])(t.ruleForm,t.id).then(h(m().m((function r(){return m().w((function(r){while(1)switch(r.n){case 0:t.$message.success("编辑成功"),t.dialogFormVisible=!1,t.$parent.tableList(),t.clearFrom(),t.resetForm(e),t.id=0;case 1:return r.a(2)}}),r)}))))}))})),clearFrom:function(){this.ruleForm.introduction="",this.dayTime=["",""]},handleChange:function(e){var t=e[0],r=e[1],n=e[2];2===e.length?this.ruleForm.address=t+","+r:3===e.length&&(this.ruleForm.address=t+","+r+","+n)},onchangeTime:function(e){this.ruleForm.dayTime=e?e.join(","):""},modalPicTap:function(e){var t=this;this.$modalUpload((function(e){t.ruleForm.image=e[0].sattDir}),e,"system")},onSearch:function(){this.modalMap=!0},selectAdderss:function(e){this.ruleForm.latitude=e.latlng.lat+","+e.latlng.lng,this.modalMap=!1},getKey:function(){var e=this,t={id:74};Object(c["b"])(t).then(function(){var t=h(m().m((function t(r){var n;return m().w((function(t){while(1)switch(t.n){case 0:n=r.tengxun_map_key,e.keyUrl="https://apis.map.qq.com/tools/locpicker?type=1&key=".concat(n,"&referer=myapp");case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},getCityList:function(){var e=this;l["c"]().then((function(t){sessionStorage.setItem("cityList",JSON.stringify(t));var r=JSON.parse(sessionStorage.getItem("cityList"));e.addresData=r})).catch((function(t){e.$message.error(t.message)}))}}},v=b,g=(r("36d0"),r("2877")),y=Object(g["a"])(v,i,o,!1,null,null,null),w=y.exports,F=r("e350"),O={name:"Point",components:{systemStore:w},data:function(){return{artFrom:{page:1,limit:20,status:"1",keywords:""},loading:!1,tableData:[],total:0,headerCount:{}}},created:function(){this.storeGetCount(),this.tableList()},methods:{checkPermi:F["a"],storeGetCount:function(){var e=this;Object(s["d"])().then((function(t){e.headerCount=t}))},tableList:function(){var e=this;e.loading=!0,Object(s["f"])(e.artFrom).then((function(t){e.loading=!1,e.tableData=t.list,e.total=t.total}))},pageChange:function(e){this.artFrom.page=e,this.tableList()},sizeChange:function(e){this.artFrom.limit=e,this.tableList()},onClickTab:function(){this.artFrom.keywords="",this.tableList()},search:function(){this.artFrom.page=1,this.tableList()},onchangeIsShow:function(e,t){var r=this;Object(s["p"])({id:e,status:t}).then((function(){r.$message.success("操作成功"),r.tableList(),r.storeGetCount()})).catch((function(){row.isShow=!row.isShow}))},storeRecovery:function(e){var t=this;this.$modalSure("恢复提货吗").then((function(){Object(s["g"])({id:e}).then((function(){t.$message.success("恢复成功"),t.storeGetCount(),t.tableList()}))}))},storeDelete:function(e){var t=this;t.$modalSure("删除提货点吗？").then((function(){Object(s["c"])({id:e}).then((function(){t.$message.success("删除成功"),t.storeGetCount(),t.tableList()}))}))},allDelete:function(e){var t=this;this.$modalSure().then((function(){Object(s["a"])({id:e}).then((function(){t.$message.success("删除成功"),t.storeGetCount(),t.tableList()}))}))},add:function(){this.$refs.template.dialogFormVisible=!0},edit:function(e){this.$refs.template.dialogFormVisible=!0,this.$refs.template.getInfo(e)}}},x=O,j=Object(g["a"])(x,n,a,!1,null,"6029dce8",null);t["default"]=j.exports},f8ef:function(e,t,r){}}]);