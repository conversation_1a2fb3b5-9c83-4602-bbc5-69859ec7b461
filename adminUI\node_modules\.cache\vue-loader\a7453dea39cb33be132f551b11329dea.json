{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue", "mtime": 1754552273980}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { mapGetters } from \"vuex\";\r\nimport Breadcrumb from \"@/components/Breadcrumb\";\r\nimport Hamburger from \"@/components/Hamburger\";\r\nimport Screenfull from \"@/components/Screenfull\";\r\nimport Search from \"@/components/HeaderSearch\";\r\nimport { unbindApi } from \"@/api/wxApi\";\r\nimport Cookies from \"js-cookie\";\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    Hamburger,\r\n    Screenfull,\r\n    Search\r\n  },\r\n  data() {\r\n    return {\r\n      isPhone: this.$wechat.isPhone(),\r\n      JavaInfo: JSON.parse(Cookies.get(\"JavaInfo\")),\r\n      languageLabels: {\r\n        \"zh-CN\": \"Chinese(中文)\",\r\n        en: \"English(英文)\",\r\n        id: \"Indonesian(印尼语)\"\r\n      },\r\n      nowLanguage: localStorage.getItem(\"locale\") || \"en\"\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"sidebar\", \"avatar\", \"device\"]),\r\n    availableLanguages() {\r\n      return Object.keys(this.languageLabels);\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch(\"app/toggleSideBar\");\r\n    },\r\n    toggleLang(lang) {\r\n      this.nowLanguage = lang;\r\n      this.$i18n.locale = lang;\r\n      localStorage.setItem(\"locale\", lang);\r\n    },\r\n    langLabel(lang) {\r\n      return this.languageLabels[lang] || lang;\r\n    },\r\n    async logout() {\r\n      await this.$store.dispatch(\"user/logout\");\r\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`);\r\n    }\r\n  }\r\n};\r\n", null]}