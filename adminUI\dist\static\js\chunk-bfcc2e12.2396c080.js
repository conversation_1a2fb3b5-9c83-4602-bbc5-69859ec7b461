(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bfcc2e12"],{9270:function(t,a,e){"use strict";e.r(a);var l=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"divBox"},[e("el-card",{staticClass:"box-card"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[e("el-table-column",{attrs:{type:"index",label:t.$t("common.serialNumber"),width:"110"}}),t._v(" "),e("el-table-column",{attrs:{label:t.$t("platformCashbackRate.platformCashbackRate")+"(%)","min-width":"80",prop:"platform_cash_back_rate"}}),t._v(" "),e("el-table-column",{attrs:{fixed:"right",label:t.$t("parameter.withdrawalFee.operation"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleEdit(a.row)}}},[t._v(t._s(t.$t("parameter.withdrawalFee.edit")))])]}}])})],1),t._v(" "),e("el-dialog",{attrs:{"append-to-body":"",visible:t.dialogFormVisible,title:t.dialogTitle,width:"680px"},on:{"update:visible":function(a){t.dialogFormVisible=a},close:t.handleCancle}},[e("el-form",{ref:"elForm",attrs:{inline:"",model:t.form,rules:t.rules,"label-width":"200px"}},[e("el-form-item",{attrs:{label:t.$t("parameter.rewardRules.rewardTemplateId"),prop:"id"}},[e("el-input",{attrs:{size:"small",disabled:""},model:{value:t.form.id,callback:function(a){t.$set(t.form,"id",a)},expression:"form.id"}})],1),t._v(" "),e("el-form-item",{attrs:{label:t.$t("platformCashbackRate.platformCashbackRate")+"(%)：",prop:"platform_cash_back_rate"}},[e("el-input",{attrs:{size:"small",placeholder:t.$t("parameter.withdrawalFee.placeholder.couponId")},model:{value:t.form.platform_cash_back_rate,callback:function(a){t.$set(t.form,"platform_cash_back_rate",a)},expression:"form.platform_cash_back_rate"}})],1)],1),t._v(" "),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:t.handelConfirm}},[t._v(t._s(t.$t("common.confirm")))]),t._v(" "),e("el-button",{on:{click:t.handleCancle}},[t._v(t._s(t.$t("common.cancel")))])],1)],1)],1)],1)},r=[],o=e("fb04"),i={name:"PlatformCashbackRate",data:function(){return{loading:!1,searchFrom:{formId:113},tableData:[],dialogTitle:this.$t("platformCashbackRate.addTitle"),dialogFormVisible:!1,form:{id:"",platform_cash_back_rate:""},rules:{platform_cash_back_rate:[{required:!0,message:this.$t("platformCashbackRate.placeholder.platformCashbackRate"),trigger:"blur"}]}}},created:function(){},mounted:function(){this.getList()},methods:{getList:function(t){var a=this;this.loading=!0,Object(o["a"])(this.searchFrom).then((function(t){t&&(a.tableData=[t]),a.loading=!1})).catch((function(){a.loading=!1}))},handleEdit:function(t){this.dialogTitle=this.$t("platformCashbackRate.editTitle"),this.dialogFormVisible=!0,this.form.id=t.id,this.form.platform_cash_back_rate=t.platform_cash_back_rate},handleCancle:function(){this.form={id:"",platform_cash_back_rate:""},this.dialogFormVisible=!1},handelConfirm:function(){var t=this;this.$refs.elForm.validate((function(a){if(a){var e={id:t.form.id,sort:1,status:!0,fields:[{name:"platform_cash_back_rate",value:t.form.platform_cash_back_rate,title:"platform_cash_back_rate"}]};Object(o["b"])(e).then((function(a){t.$message.success(t.$t("common.operationSuccess")),t.handleCancle(),t.getList()}))}}))}}},s=i,n=e("2877"),c=Object(n["a"])(s,l,r,!1,null,"3386a8c6",null);a["default"]=c.exports},fb04:function(t,a,e){"use strict";e.d(a,"a",(function(){return r})),e.d(a,"b",(function(){return o}));var l=e("b775");function r(t){return Object(l["a"])({url:"/admin/system/config/info",method:"get",params:t})}function o(t){return Object(l["a"])({url:"/admin/system/config/save/form",method:"post",data:t})}}}]);