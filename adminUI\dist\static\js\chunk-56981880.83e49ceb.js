(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56981880"],{"2eb3":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return s})),n.d(t,"d",(function(){return l})),n.d(t,"l",(function(){return o})),n.d(t,"k",(function(){return m})),n.d(t,"i",(function(){return u})),n.d(t,"f",(function(){return d})),n.d(t,"g",(function(){return c})),n.d(t,"h",(function(){return p})),n.d(t,"j",(function(){return h}));var a=n("b775");function i(e){var t={id:e.id};return Object(a["a"])({url:"/admin/system/admin/delete",method:"GET",params:t})}function r(e){return Object(a["a"])({url:"/admin/system/admin/list",method:"GET",params:e})}function s(e){var t={account:e.account,level:e.level,pwd:e.pwd,realName:e.realName,roles:e.roles.join(","),status:e.status,phone:e.phone};return Object(a["a"])({url:"/admin/system/admin/save",method:"POST",data:t})}function l(e){var t={account:e.account,level:e.level,pwd:e.pwd,roles:e.roles,realName:e.realName,status:e.status,id:e.id,isDel:e.isDel};return Object(a["a"])({url:"/admin/system/admin/update",method:"POST",data:t})}function o(e){return Object(a["a"])({url:"/admin/system/admin/updateStatus",method:"get",params:e})}function m(e){return Object(a["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:e})}function u(e){var t={menuType:e.menuType,name:e.name};return Object(a["a"])({url:"/admin/system/menu/list",method:"get",params:t})}function d(e){var t=e;return Object(a["a"])({url:"/admin/system/menu/add",method:"post",data:t})}function c(e){return Object(a["a"])({url:"/admin/system/menu/delete/".concat(e),method:"post"})}function p(e){return Object(a["a"])({url:"/admin/system/menu/info/".concat(e),method:"get"})}function h(e){var t=e;return Object(a["a"])({url:"/admin/system/menu/update",method:"post",data:t})}},a391:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"divBox"},[n("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(e){e.preventDefault()}}},[n("el-form-item",[n("el-select",{staticClass:"selWidth",attrs:{placeholder:e.$t("admin.system.admin.role"),clearable:""},model:{value:e.listPram.roles,callback:function(t){e.$set(e.listPram,"roles",t)},expression:"listPram.roles"}},e._l(e.roleList.list,(function(e){return n("el-option",{key:e.id,attrs:{label:e.roleName,value:e.id}})})),1)],1),e._v(" "),n("el-form-item",[n("el-select",{staticClass:"selWidth",attrs:{placeholder:e.$t("admin.system.admin.status"),clearable:""},model:{value:e.listPram.status,callback:function(t){e.$set(e.listPram,"status",t)},expression:"listPram.status"}},e._l(e.constants.roleListStatus,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),n("el-form-item",[n("el-input",{staticClass:"selWidth",attrs:{placeholder:e.$t("admin.system.admin.realName"),clearable:""},model:{value:e.listPram.realName,callback:function(t){e.$set(e.listPram,"realName",t)},expression:"listPram.realName"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.handleSearch}},[e._v(e._s(e.$t("common.query")))])],1)],1),e._v(" "),n("el-form",{attrs:{inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[n("el-form-item",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:save"],expression:"['admin:system:admin:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handlerOpenEdit(0)}}},[e._v("\n        "+e._s(e.$t("admin.system.admin.addAdmin"))+"\n      ")])],1)],1),e._v(" "),n("el-table",{attrs:{data:e.listData.list,size:"mini","header-cell-style":{fontWeight:"bold"}}},[n("el-table-column",{attrs:{prop:"id",label:e.$t("admin.system.admin.id"),width:"50"}}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.realName"),prop:"realName","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.account"),prop:"account","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.phone"),prop:"lastTime","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.role"),prop:"realName","min-width":"230"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.roleNames?e._l(t.row.roleNames.split(","),(function(t,a){return n("el-tag",{key:a,staticClass:"mr5",attrs:{size:"small",type:"info"}},[e._v(e._s(t))])})):void 0}}],null,!0)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.lastTime"),prop:"lastTime","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("filterEmpty")(t.row.lastTime)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.lastIp"),prop:"lastIp","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("filterEmpty")(t.row.lastIp)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.status"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["admin:system:admin:update:status"])?[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":e.$t("common.yes"),"inactive-text":e.$t("common.no")},on:{change:function(n){return e.onchangeIsShow(t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.isSms"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["admin:system:admin:update:sms"])?[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":e.$t("common.yes"),"inactive-text":e.$t("common.no"),disabled:!t.row.phone},nativeOn:{click:function(n){return e.onchangeIsSms(t.row)}},model:{value:t.row.isSms,callback:function(n){e.$set(t.row,"isSms",n)},expression:"scope.row.isSms"}})]:void 0}}],null,!0)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.isDel"),prop:"status","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("filterYesOrNo")(t.row.isDel)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("admin.system.admin.operation"),"min-width":"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isDel?[n("span",[e._v("-")])]:[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:info"],expression:"['admin:system:admin:info']"}],attrs:{type:"text",size:"mini"},on:{click:function(n){return e.handlerOpenEdit(1,t.row)}}},[e._v("\n            "+e._s(e.$t("admin.system.admin.edit"))+"\n          ")]),e._v(" "),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:delete"],expression:"['admin:system:admin:delete']"}],attrs:{type:"text",size:"mini"},on:{click:function(n){return e.handlerOpenDel(t.row)}}},[e._v("\n            "+e._s(e.$t("admin.system.admin.delete"))+"\n          ")])]]}}])})],1),e._v(" "),n("el-dialog",{attrs:{visible:e.editDialogConfig.visible,title:0===e.editDialogConfig.isCreate?e.$t("admin.system.admin.createIdentity"):e.$t("admin.system.admin.editIdentity"),"destroy-on-close":"","close-on-click-modal":!1,width:"700px"},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?n("edit",{attrs:{"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.editData},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],1)},i=[],r=n("2eb3"),s=n("cc5e"),l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"pram",attrs:{model:e.pram,rules:e.rules,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[n("el-form-item",{attrs:{label:e.$t("admin.system.admin.account"),prop:"account"}},[n("el-input",{attrs:{placeholder:e.$t("admin.system.admin.account")},model:{value:e.pram.account,callback:function(t){e.$set(e.pram,"account",t)},expression:"pram.account"}})],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("admin.system.admin.pwd"),prop:"pwd"}},[n("el-input",{attrs:{placeholder:e.$t("admin.system.admin.pwd"),clearable:""},on:{input:e.handlerPwdInput,clear:e.handlerPwdInput},model:{value:e.pram.pwd,callback:function(t){e.$set(e.pram,"pwd",t)},expression:"pram.pwd"}})],1),e._v(" "),e.pram.pwd?n("el-form-item",{attrs:{label:e.$t("admin.system.admin.repwd"),prop:"repwd"}},[n("el-input",{attrs:{placeholder:e.$t("admin.system.admin.repwd"),clearable:""},model:{value:e.pram.repwd,callback:function(t){e.$set(e.pram,"repwd",t)},expression:"pram.repwd"}})],1):e._e(),e._v(" "),n("el-form-item",{attrs:{label:e.$t("admin.system.admin.realName"),prop:"realName"}},[n("el-input",{attrs:{placeholder:e.$t("admin.system.admin.realName")},model:{value:e.pram.realName,callback:function(t){e.$set(e.pram,"realName",t)},expression:"pram.realName"}})],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("admin.system.admin.roles"),prop:"roles"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:e.$t("admin.system.admin.roles"),clearable:"",multiple:""},model:{value:e.pram.roles,callback:function(t){e.$set(e.pram,"roles",t)},expression:"pram.roles"}},e._l(e.roleList.list,(function(e,t){return n("el-option",{key:t,attrs:{label:e.roleName,value:e.id}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("admin.system.admin.phone"),prop:"phone"}},[n("el-input",{attrs:{type:"text",prefix:"ios-contact-outline",placeholder:e.$t("admin.system.admin.phone"),size:"large"},model:{value:e.pram.phone,callback:function(t){e.$set(e.pram,"phone",t)},expression:"pram.phone"}})],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("common.status")}},[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:e.pram.status,callback:function(t){e.$set(e.pram,"status",t)},expression:"pram.status"}})],1),e._v(" "),n("el-form-item",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:update","admin:system:admin:save"],expression:"['admin:system:admin:update', 'admin:system:admin:save']"}],attrs:{type:"primary"},on:{click:function(t){return e.handlerSubmit("pram")}}},[e._v(e._s(0===e.isCreate?e.$t("common.confirm"):e.$t("common.update")))]),e._v(" "),n("el-button",{on:{click:e.close}},[e._v(e._s(e.$t("common.cancel")))])],1)],1)],1)},o=[],m=n("61f7");function u(e){return h(e)||p(e)||c(e)||d()}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function h(e){if(Array.isArray(e))return f(e)}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var v={components:{},props:{isCreate:{type:Number,required:!0},editData:{type:Object,default:function(){return{rules:[]}}}},data:function(){var e=this,t=function(t,n,a){""===n?a(new Error(e.$t("admin.system.admin.validatePass.required"))):n!==e.pram.pwd?a(new Error(e.$t("admin.system.admin.validatePass.notMatch"))):a()};return{constants:this.$constants,pram:{account:null,level:null,pwd:null,repwd:null,realName:null,roles:[],status:null,id:null,phone:null},roleList:[],rules:{account:[{required:!0,message:this.$t("admin.system.admin.validateAccount.required"),trigger:["blur","change"]}],pwd:[{required:!0,message:this.$t("admin.system.admin.validatePassword.required"),trigger:["blur","change"]}],repwd:[{required:!0,message:this.$t("admin.system.admin.validateConfirmPassword.required"),validator:t,trigger:["blur","change"]}],realName:[{required:!0,message:this.$t("admin.system.admin.validateRealName.required"),trigger:["blur","change"]}],roles:[{required:!0,message:this.$t("admin.system.admin.validateRoles.required"),trigger:["blur","change"]}],phone:[{required:!0,message:this.$t("admin.system.admin.validatePhone.required"),trigger:["blur","change"]}]}}},mounted:function(){this.initEditData(),this.handleGetRoleList()},methods:{close:function(){this.$emit("hideEditDialog")},handleGetRoleList:function(){var e=this,t={page:1,limit:this.constants.page.limit[4],status:1};s["d"](t).then((function(t){e.roleList=t;var n=[];t.list.forEach((function(e){n.push(e.id)})),n.includes(Number.parseInt(e.pram.roles))||e.$set(e.pram,"roles",[])}))},initEditData:function(){if(1===this.isCreate){var e=this.editData,t=e.account,n=e.realName,a=e.roles,i=(e.level,e.status),r=e.id,s=e.phone;this.pram.account=t,this.pram.realName=n;var l=[];a.length>0&&!a.includes(",")?l.push(Number.parseInt(a)):l.push.apply(l,u(a.split(",").map((function(e){return Number.parseInt(e)})))),this.pram.roles=l,this.pram.status=i,this.pram.id=r,this.pram.phone=s,this.rules.pwd=[],this.rules.repwd=[]}},handlerSubmit:Object(m["a"])((function(e){var t=this;this.$refs[e].validate((function(e){e&&(0===t.isCreate?t.handlerSave():t.handlerEdit())}))})),handlerSave:function(){var e=this;r["a"](this.pram).then((function(t){e.$message.success(e.$t("admin.system.admin.message.createSuccess")),e.$emit("hideEditDialog")}))},handlerEdit:function(){var e=this;this.pram.roles=this.pram.roles.join(","),r["d"](this.pram).then((function(t){e.$message.success(e.$t("admin.system.admin.message.updateSuccess")),e.$emit("hideEditDialog")}))},rulesSelect:function(e){this.pram.rules=e},handlerPwdInput:function(e){var t=this;if(!e)return this.rules.pwd=[],void(this.rules.repwd=[]);this.rules.pwd=[{required:!0,message:this.$t("admin.system.admin.validatePassword.required"),trigger:["blur","change"]},{min:6,max:20,message:this.$t("admin.system.admin.validatePassword.lengthError"),trigger:["blur","change"]}],this.rules.repwd=[{required:!0,message:this.$t("admin.system.admin.validateConfirmPassword.required"),validator:function(e,n,a){""===n||n!==t.pram.pwd?a(new Error(t.$t("admin.system.admin.validatePass.notMatch"))):a()},trigger:["blur","change"]}]}}},y=v,b=n("2877"),g=Object(b["a"])(y,l,o,!1,null,"4a1c684c",null),w=g.exports,$=n("e350");function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,a,i,r){var o=a&&a.prototype instanceof l?a:l,m=Object.create(o.prototype);return P(m,"_invoke",function(n,a,i){var r,l,o,m=0,u=i||[],d=!1,c={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return r=t,l=0,o=e,c.n=n,s}};function p(n,a){for(l=n,o=a,t=0;!d&&m&&!i&&t<u.length;t++){var i,r=u[t],p=c.p,h=r[2];n>3?(i=h===a)&&(o=r[(l=r[4])?5:(l=3,3)],r[4]=r[5]=e):r[0]<=p&&((i=n<2&&p<r[1])?(l=0,c.v=a,c.n=r[1]):p<h&&(i=n<3||r[0]>a||a>h)&&(r[4]=n,r[5]=a,c.n=h,l=0))}if(i||n>1)return s;throw d=!0,a}return function(i,u,h){if(m>1)throw TypeError("Generator is already running");for(d&&1===u&&p(u,h),l=u,o=h;(t=l<2?e:o)||!d;){r||(l?l<3?(l>1&&(c.n=-1),p(l,o)):c.n=o:c.v=o);try{if(m=2,r){if(l||(i="next"),t=r[i]){if(!(t=t.call(r,o)))throw TypeError("iterator result is not an object");if(!t.done)return t;o=t.value,l<2&&(l=0)}else 1===l&&(t=r.return)&&t.call(r),l<2&&(o=TypeError("The iterator does not provide a '"+i+"' method"),l=1);r=e}else if((t=(d=c.n<0)?o:n.call(a,c))!==s)break}catch(t){r=e,l=1,o=t}finally{m=1}}return{value:t,done:d}}}(n,i,r),!0),m}var s={};function l(){}function o(){}function m(){}t=Object.getPrototypeOf;var u=[][a]?t(t([][a]())):(P(t={},a,(function(){return this})),t),d=m.prototype=l.prototype=Object.create(u);function c(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,P(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return o.prototype=m,P(d,"constructor",m),P(m,"constructor",o),o.displayName="GeneratorFunction",P(m,i,"GeneratorFunction"),P(d),P(d,i,"Generator"),P(d,a,(function(){return this})),P(d,"toString",(function(){return"[object Generator]"})),(_=function(){return{w:r,m:c}})()}function P(e,t,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}P=function(e,t,n,a){function r(t,n){P(e,t,(function(e){return this._invoke(t,n,e)}))}t?i?i(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n:(r("next",0),r("throw",1),r("return",2))},P(e,t,n,a)}function S(e,t,n,a,i,r,s){try{var l=e[r](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(a,i)}function O(e){return function(){var t=this,n=arguments;return new Promise((function(a,i){var r=e.apply(t,n);function s(e){S(r,a,i,s,l,"next",e)}function l(e){S(r,a,i,s,l,"throw",e)}s(void 0)}))}}var k={components:{edit:w},data:function(){return{constants:this.$constants,listData:{list:[]},listPram:{account:null,addTime:null,lastIp:null,lastTime:null,level:null,loginCount:null,realName:null,roles:null,status:null,page:1,limit:this.$constants.page.limit[0]},roleList:[],menuList:[],editDialogConfig:{visible:!1,isCreate:0,editData:{}}}},mounted:function(){this.handleGetAdminList(),this.handleGetRoleList()},methods:{checkPermi:$["a"],onchangeIsShow:function(e){var t=this;r["l"]({id:e.id,status:e.status}).then(O(_().m((function e(){return _().w((function(e){while(1)switch(e.n){case 0:t.$message.success(t.$t("common.operationSuccess")),t.handleGetAdminList();case 1:return e.a(2)}}),e)})))).catch((function(){e.status=!e.status}))},onchangeIsSms:function(e){var t=this;if(!e.phone)return this.$message({message:this.$t("admin.system.admin.pleaseAddPhone"),type:"warning"});r["k"]({id:e.id}).then(O(_().m((function e(){return _().w((function(e){while(1)switch(e.n){case 0:t.$message.success(t.$t("common.operationSuccess")),t.handleGetAdminList();case 1:return e.a(2)}}),e)})))).catch((function(){e.isSms=!e.isSms}))},handleSearch:function(){this.listPram.page=1,this.handleGetAdminList()},handleSizeChange:function(e){this.listPram.limit=e,this.handleGetAdminList(),this.handleGetRoleList(this.listPram)},handleCurrentChange:function(e){this.listPram.page=e,this.handleGetAdminList(),this.handleGetRoleList(this.listPram)},handleGetRoleList:function(){var e=this,t={page:1,limit:this.constants.page.limit[4]};s["d"](t).then((function(t){e.roleList=t}))},handlerOpenDel:function(e){var t=this;this.$confirm(this.$t("admin.system.admin.confirmDelete")).then((function(){var n={id:e.id};r["b"](n).then((function(e){t.$message.success(t.$t("common.prompt")),t.handleGetAdminList()}))}))},handleGetAdminList:function(){var e=this;r["c"](this.listPram).then((function(t){e.listData=t}))},handlerOpenEdit:function(e,t){this.editDialogConfig.editData=t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerGetMenuList:function(){var e=this;r["listCategroy"]({page:1,limit:999,type:5}).then((function(t){e.menuList=t.list,e.listData.list.forEach((function(t){var n=[],a=t.rules.split(",");a.map((function(t){e.menuList.filter((function(e){e.id==t&&n.push(e.name)}))})),t.rulesView=n.join(","),e.$set(t,"rulesViews",t.rulesView)}))}))},hideEditDialog:function(){this.editDialogConfig.visible=!1,this.handleGetAdminList()}}},N=k,D=Object(b["a"])(N,a,i,!1,null,"54a59566",null);t["default"]=D.exports},cc5e:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return l})),n.d(t,"f",(function(){return o})),n.d(t,"g",(function(){return m})),n.d(t,"e",(function(){return u}));var a=n("b775");function i(e){var t={level:e.level,roleName:e.roleName,status:e.status,rules:e.rules};return Object(a["a"])({url:"/admin/system/role/save",method:"POST",data:t})}function r(e){var t={id:e.id};return Object(a["a"])({url:"/admin/system/role/delete",method:"GET",params:t})}function s(e){return Object(a["a"])({url:"/admin/system/role/info/".concat(e),method:"GET"})}function l(e){var t={createTime:e.createTime,updateTime:e.updateTime,level:e.level,page:e.page,limit:e.limit,roleName:e.roleName,rules:e.rules,status:e.status};return Object(a["a"])({url:"/admin/system/role/list",method:"get",params:t})}function o(e){var t={id:e.id,roleName:e.roleName,rules:e.rules,status:e.status};return Object(a["a"])({url:"/admin/system/role/update",method:"post",params:{id:e.id},data:t})}function m(e){return Object(a["a"])({url:"/admin/system/role/updateStatus",method:"get",params:{id:e.id,status:e.status}})}function u(e){return Object(a["a"])({url:"/admin/system/menu/cache/tree",method:"get"})}}}]);