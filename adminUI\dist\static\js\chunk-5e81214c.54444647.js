(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5e81214c"],{"47ca":function(e,r,a){"use strict";a.r(r);var t=function(){var e=this,r=e.$createElement,a=e._self._c||r;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"index",label:e.$t("common.serialNumber"),width:"110"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:e.$t("parameter.referralRewardConfig.rewardTemplateId"),"min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:e.$t("parameter.referralRewardConfig.rewardTemplateName"),"min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"value1",label:e.$t("parameter.referralRewardConfig.referralCount"),"min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"value2",label:e.$t("parameter.referralRewardConfig.firstOrderCount"),"min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"value3",label:e.$t("parameter.referralRewardConfig.rewardAmount"),"min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"reward_rule_zh",label:e.$t("parameter.referralRewardConfig.rewardRuleZh"),"min-width":"150","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"reward_rule_en",label:e.$t("parameter.referralRewardConfig.rewardRuleEn"),"min-width":"150","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"reward_rule_id",label:e.$t("parameter.referralRewardConfig.rewardRuleId"),"min-width":"150","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("parameter.referralRewardConfig.operation"),"min-width":"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[a("el-button",{attrs:{size:"small",type:"text"},on:{click:function(a){return e.handleEdit(r.row)}}},[e._v(e._s(e.$t("parameter.referralRewardConfig.edit")))])]}}])})],1),e._v(" "),a("el-dialog",{attrs:{"append-to-body":"",visible:e.dialogFormVisible,title:e.$t("parameter.referralRewardConfig.editTitle"),width:"800px"},on:{"update:visible":function(r){e.dialogFormVisible=r},close:e.handleCancle}},[a("el-tabs",{attrs:{type:"border-card"},model:{value:e.activeTab,callback:function(r){e.activeTab=r},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:e.$t("parameter.referralRewardConfig.basicConfig"),name:"basic"}},[a("el-form",{ref:"elForm",attrs:{inline:"",model:e.artFrom,rules:e.rules,"label-width":"200px"}},[a("el-form-item",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardTemplateId"),prop:"id"}},[a("el-input",{attrs:{size:"small",disabled:!0,placeholder:e.$t("common.enter")},model:{value:e.artFrom.id,callback:function(r){e.$set(e.artFrom,"id",r)},expression:"artFrom.id"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardTemplateName"),prop:"name"}},[a("el-input",{attrs:{size:"small",disabled:!0,placeholder:e.$t("common.enter")},model:{value:e.artFrom.name,callback:function(r){e.$set(e.artFrom,"name",r)},expression:"artFrom.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("parameter.referralRewardConfig.referralCount"),prop:"value1"}},[a("el-input",{attrs:{size:"small",type:"number",placeholder:e.$t("common.enter")},model:{value:e.artFrom.value1,callback:function(r){e.$set(e.artFrom,"value1",r)},expression:"artFrom.value1"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("parameter.referralRewardConfig.firstOrderCount"),prop:"value2"}},[a("el-input",{attrs:{size:"small",type:"number",placeholder:e.$t("common.enter")},model:{value:e.artFrom.value2,callback:function(r){e.$set(e.artFrom,"value2",r)},expression:"artFrom.value2"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardAmount"),prop:"value3"}},[a("el-input",{attrs:{size:"small",type:"number",placeholder:e.$t("common.enter")},model:{value:e.artFrom.value3,callback:function(r){e.$set(e.artFrom,"value3",r)},expression:"artFrom.value3"}})],1)],1)],1),e._v(" "),a("el-tab-pane",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardRuleZh"),name:"zh"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",{staticStyle:{width:"100%","max-width":"100%","box-sizing":"border-box"}},[a("Tinymce",{attrs:{toolbar:e.tinymceToolbar,height:400},model:{value:e.artFrom.reward_rule_zh,callback:function(r){e.$set(e.artFrom,"reward_rule_zh",r)},expression:"artFrom.reward_rule_zh"}})],1)])]),e._v(" "),a("el-tab-pane",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardRuleEn"),name:"en"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",{staticStyle:{width:"100%","max-width":"100%","box-sizing":"border-box"}},[a("Tinymce",{attrs:{toolbar:e.tinymceToolbar,height:400},model:{value:e.artFrom.reward_rule_en,callback:function(r){e.$set(e.artFrom,"reward_rule_en",r)},expression:"artFrom.reward_rule_en"}})],1)])]),e._v(" "),a("el-tab-pane",{attrs:{label:e.$t("parameter.referralRewardConfig.rewardRuleId"),name:"id"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",{staticStyle:{width:"100%","max-width":"100%","box-sizing":"border-box"}},[a("Tinymce",{attrs:{toolbar:e.tinymceToolbar,height:400},model:{value:e.artFrom.reward_rule_id,callback:function(r){e.$set(e.artFrom,"reward_rule_id",r)},expression:"artFrom.reward_rule_id"}})],1)])])],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v(e._s(e.$t("common.confirm")))]),e._v(" "),a("el-button",{on:{click:e.handleCancle}},[e._v(e._s(e.$t("common.cancel")))])],1)],1)],1)],1)},l=[],i=a("fb04"),o=a("8256"),n={name:"ReferralRewardConfig",components:{Tinymce:o["a"]},data:function(){return{tinymceToolbar:["fontsizeselect fontselect searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent  blockquote undo redo","removeformat subscript superscript code codesample hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media ","table emoticons forecolor backcolor"],loading:!1,tableData:[],dialogFormVisible:!1,activeTab:"basic",artFrom:{id:"",name:"",value1:"",value2:"",value3:"",reward_rule_zh:"",reward_rule_en:"",reward_rule_id:""},rules:{value1:[{required:!0,message:this.$t("common.enter"),trigger:"blur"},{validator:this.validateReferralCount,trigger:"blur"}],value2:[{required:!0,message:this.$t("common.enter"),trigger:"blur"},{validator:this.validateFirstOrderCount,trigger:"blur"}],value3:[{required:!0,message:this.$t("common.enter"),trigger:"blur"},{validator:this.validateRewardAmount,trigger:"blur"}]}}},created:function(){},mounted:function(){this.getList()},methods:{validateReferralCount:function(e,r,a){r<0?a(new Error(this.$t("parameter.referralRewardConfig.validation.referralCountMin"))):a()},validateFirstOrderCount:function(e,r,a){r<0?a(new Error(this.$t("parameter.referralRewardConfig.validation.firstOrderCountMin"))):parseInt(r)>=parseInt(this.artFrom.value1)?a(new Error(this.$t("parameter.referralRewardConfig.validation.firstOrderCountMax"))):a()},validateRewardAmount:function(e,r,a){r<0?a(new Error(this.$t("parameter.referralRewardConfig.validation.rewardAmountMin"))):a()},getList:function(){var e=this;this.tableData=[],this.loading=!0,Object(i["a"])({formId:"144"}).then((function(r){r&&e.tableData.push({id:r.id,name:r.template_name,value1:r.referral_count,value2:r.first_order_count,value3:r.reward_amount,reward_rule_zh:r.reward_rule_zh||"",reward_rule_en:r.reward_rule_en||"",reward_rule_id:r.reward_rule_id||""}),e.loading=!1})).catch((function(){e.loading=!1}))},handleEdit:function(e){this.artFrom={id:e.id,name:e.name,value1:e.value1,value2:e.value2,value3:e.value3,reward_rule_zh:e.reward_rule_zh||"",reward_rule_en:e.reward_rule_en||"",reward_rule_id:e.reward_rule_id||""},this.dialogFormVisible=!0},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(r){if(r){var a={id:e.artFrom.id,sort:1,status:!0,fields:[{name:"template_name",value:e.artFrom.name,title:"template_name"},{name:"referral_count",value:e.artFrom.value1,title:"referral_count"},{name:"first_order_count",value:e.artFrom.value2,title:"first_order_count"},{name:"reward_amount",value:e.artFrom.value3,title:"reward_amount"},{name:"reward_rule_zh",value:e.artFrom.reward_rule_zh,title:"reward_rule_zh"},{name:"reward_rule_en",value:e.artFrom.reward_rule_en,title:"reward_rule_en"},{name:"reward_rule_id",value:e.artFrom.reward_rule_id,title:"reward_rule_id"}]};Object(i["b"])(a).then((function(){e.$message.success(e.$t("common.saveSuccess")),e.dialogFormVisible=!1,e.getList()})).catch((function(){e.$message.error(e.$t("common.saveFailed"))}))}}))},handleCancle:function(){this.dialogFormVisible=!1,this.activeTab="basic",this.artFrom={id:"",name:"",value1:"",value2:"",value3:"",reward_rule_zh:"",reward_rule_en:"",reward_rule_id:""}}}},d=n,m=(a("6fe2"),a("2877")),u=Object(m["a"])(d,t,l,!1,null,"0f0b9ca2",null);r["default"]=u.exports},"6fe2":function(e,r,a){"use strict";a("8e49")},"8e49":function(e,r,a){},fb04:function(e,r,a){"use strict";a.d(r,"a",(function(){return l})),a.d(r,"b",(function(){return i}));var t=a("b775");function l(e){return Object(t["a"])({url:"/admin/system/config/info",method:"get",params:e})}function i(e){return Object(t["a"])({url:"/admin/system/config/save/form",method:"post",data:e})}}}]);