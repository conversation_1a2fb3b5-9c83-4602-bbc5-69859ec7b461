(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f369632c"],{"0ae0":function(t,e,i){},a58b:function(t,e,i){"use strict";i("0ae0")},ba20:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox relative"},[i("el-card",{staticClass:"box-card"},[i("el-form",{ref:"searchFormRef",attrs:{model:t.searchForm,rules:t.searchRules,inline:"",size:"small"}},[i("el-form-item",{attrs:{label:t.$t("affiliateProducts.priceRange")}},[i("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:t.$t("affiliateProducts.minPrice")},model:{value:t.searchForm.priceMin,callback:function(e){t.$set(t.searchForm,"priceMin",e)},expression:"searchForm.priceMin"}}),t._v(" "),i("span",{staticStyle:{margin:"0 8px"}},[t._v("-")]),t._v(" "),i("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:t.$t("affiliateProducts.maxPrice")},model:{value:t.searchForm.priceMax,callback:function(e){t.$set(t.searchForm,"priceMax",e)},expression:"searchForm.priceMax"}})],1),t._v(" "),i("el-form-item",{attrs:{label:t.$t("affiliateProducts.commissionRange"),prop:"commissionRange"}},[i("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:t.$t("affiliateProducts.minCommission")},on:{blur:function(e){return t.validateCommissionRate("commissionMin")},input:function(e){return t.clearCommissionError("commissionMin")}},model:{value:t.searchForm.commissionMin,callback:function(e){t.$set(t.searchForm,"commissionMin",e)},expression:"searchForm.commissionMin"}}),t._v(" "),i("span",{staticStyle:{margin:"0 8px"}},[t._v("-")]),t._v(" "),i("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:t.$t("affiliateProducts.maxCommission")},on:{blur:function(e){return t.validateCommissionRate("commissionMax")},input:function(e){return t.clearCommissionError("commissionMax")}},model:{value:t.searchForm.commissionMax,callback:function(e){t.$set(t.searchForm,"commissionMax",e)},expression:"searchForm.commissionMax"}}),t._v(" "),t.commissionErrors.commissionMin?i("div",{staticClass:"commission-error"},[t._v("\n          "+t._s(t.commissionErrors.commissionMin)+"\n        ")]):t._e(),t._v(" "),t.commissionErrors.commissionMax?i("div",{staticClass:"commission-error"},[t._v("\n          "+t._s(t.commissionErrors.commissionMax)+"\n        ")]):t._e()],1),t._v(" "),i("el-form-item",{attrs:{label:t.$t("affiliateProducts.sort")}},[i("el-select",{staticStyle:{width:"140px"},model:{value:t.searchForm.sortField,callback:function(e){t.$set(t.searchForm,"sortField",e)},expression:"searchForm.sortField"}},[i("el-option",{attrs:{label:t.$t("affiliateProducts.sortCommissionRate"),value:"commission_rate"}}),t._v(" "),i("el-option",{attrs:{label:t.$t("affiliateProducts.sortCommission"),value:"commission"}}),t._v(" "),i("el-option",{attrs:{label:t.$t("affiliateProducts.sortPrice"),value:"product_sales_price"}}),t._v(" "),i("el-option",{attrs:{label:t.$t("affiliateProducts.sortSales"),value:"units_sold"}})],1),t._v(" "),i("el-select",{staticStyle:{width:"80px","margin-left":"8px"},model:{value:t.searchForm.sortOrder,callback:function(e){t.$set(t.searchForm,"sortOrder",e)},expression:"searchForm.sortOrder"}},[i("el-option",{attrs:{label:t.$t("affiliateProducts.sortDesc"),value:"DESC"}}),t._v(" "),i("el-option",{attrs:{label:t.$t("affiliateProducts.sortAsc"),value:"ASC"}})],1)],1),t._v(" "),i("el-form-item",{attrs:{label:t.$t("affiliateProducts.keywords")}},[i("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("affiliateProducts.addKeywordPlaceholder"),clearable:"",disabled:t.searchForm.keywords.length>=20},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.addKeyword(e)}},model:{value:t.currentKeyword,callback:function(e){t.currentKeyword=e},expression:"currentKeyword"}},[i("template",{slot:"append"},[i("span",{staticClass:"keyword-count",class:{"keyword-count-warning":t.searchForm.keywords.length>=18}},[t._v("\n              "+t._s(t.searchForm.keywords.length)+"/20\n            ")])])],2),t._v(" "),t.keywordError?i("div",{staticClass:"keyword-error",staticStyle:{"margin-top":"4px"}},[t._v("\n          "+t._s(t.keywordError)+"\n        ")]):t._e()],1)],1),t._v(" "),i("div",{staticClass:"search-actions"},[i("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.handleSearch}},[t._v(t._s(t.$t("affiliateProducts.query")))]),t._v(" "),i("el-button",{attrs:{size:"small"},on:{click:t.handleReset}},[t._v(t._s(t.$t("affiliateProducts.reset")))])],1),t._v(" "),t.searchForm.keywords.length>0?i("div",{staticClass:"keywords-display-section",staticStyle:{"margin-top":"16px"}},[i("div",{staticClass:"keywords-display-header"},[i("div",{staticClass:"keywords-display-label"},[t._v(t._s(t.$t("affiliateProducts.selectedKeywords")))]),t._v(" "),i("el-button",{staticClass:"clear-all-btn",attrs:{size:"mini",type:"text",icon:"el-icon-delete",title:t.$t("affiliateProducts.clearAllKeywords")},on:{click:t.clearAllKeywords}},[t._v("\n          "+t._s(t.$t("affiliateProducts.clearAll"))+"\n        ")])],1),t._v(" "),i("div",{staticClass:"keywords-display-container"},t._l(t.searchForm.keywords,(function(e,s){return i("el-tag",{key:s,staticClass:"keyword-display-tag",attrs:{closable:""},on:{close:function(e){return t.removeKeyword(s)}}},[t._v("\n          "+t._s(e)+"\n        ")])})),1)]):t._e()],1),t._v(" "),i("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"12px"}},[t.hasSearched&&t.tableData.length>0?i("div",{staticStyle:{"margin-bottom":"15px"}},[i("el-button",{attrs:{type:"primary",size:"small",disabled:0===t.selectedProducts.length||t.batchImporting},on:{click:t.handleBatchImport}},[t._v("\n        "+t._s(t.batchImporting?t.$t("affiliateProducts.batchImporting"):t.$t("affiliateProducts.batchImport")+" ("+t.selectedProducts.length+")")+"\n      ")]),t._v(" "),i("el-button",{attrs:{type:"danger",size:"small",disabled:0===t.selectedProducts.length||t.batchDeleting},on:{click:t.handleBatchDelete}},[t._v("\n        "+t._s(t.batchDeleting?t.$t("affiliateProducts.batchDeleting"):t.$t("affiliateProducts.batchDelete")+" ("+t.selectedProducts.length+")")+"\n      ")])],1):t._e(),t._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"productTable",attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),i("el-table-column",{attrs:{type:"index",label:t.$t("affiliateProducts.serialNumber"),width:"60"}}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.productImage"),width:"100"},scopedSlots:t._u([{key:"default",fn:function(t){return[i("el-image",{staticStyle:{width:"60px",height:"60px","border-radius":"4px"},attrs:{src:t.row.mainImageUrl,fit:"cover","preview-src-list":[t.row.mainImageUrl]}},[i("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[i("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.productTitle"),"min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-link",{attrs:{href:e.row.detailLink,target:"_blank",type:"primary"}},[t._v("\n            "+t._s(e.row.title)+"\n          ")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.shop"),width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.shop?e.row.shop.name:"-")+"\n        ")]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.originalPrice"),width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.originalPrice?i("span",[t._v("\n            "+t._s(t.formatPrice(e.row.originalPrice))+"\n          ")]):i("span",[t._v("-")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.salesPrice"),width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.salesPrice?i("span",[t._v("\n            "+t._s(t.formatPrice(e.row.salesPrice))+"\n          ")]):i("span",[t._v("-")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.commissionRate"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.commission?i("span",[t._v("\n            "+t._s(t.formatCommissionRate(e.row.commission.rate))+"%\n          ")]):i("span",[t._v("-")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.commissionAmount"),width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.commission?i("span",[t._v("\n            "+t._s(e.row.commission.amount)+" "+t._s(e.row.commission.currency)+"\n          ")]):i("span",[t._v("-")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.unitsSold"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.unitsSold||0)+"\n        ")]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.inventoryStatus"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-tag",{attrs:{type:e.row.hasInventory?"success":"danger",size:"mini"}},[t._v("\n            "+t._s(e.row.hasInventory?t.$t("affiliateProducts.hasInventory"):t.$t("affiliateProducts.noInventory"))+"\n          ")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.saleRegion"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.saleRegion||"-")+"\n        ")]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.importStatus"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-tag",{attrs:{type:e.row.isImported?"success":"info",size:"mini"}},[t._v("\n            "+t._s(e.row.isImported?t.$t("affiliateProducts.imported"):t.$t("affiliateProducts.notImported"))+"\n          ")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:t.$t("affiliateProducts.action"),width:"180",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:e.row.isImported?"success":"primary",size:"mini",disabled:e.row.importing||e.row.isImported},on:{click:function(i){return t.handleImportProduct(e.row)}}},[t._v("\n            "+t._s(e.row.importing?t.$t("affiliateProducts.importing"):e.row.isImported?t.$t("affiliateProducts.imported"):t.$t("affiliateProducts.import"))+"\n          ")]),t._v(" "),i("el-button",{attrs:{type:"danger",size:"mini",disabled:e.row.deleting},on:{click:function(i){return t.handleDeleteProduct(e.row)}}},[t._v("\n            "+t._s(e.row.deleting?t.$t("affiliateProducts.deleting"):t.$t("affiliateProducts.delete"))+"\n          ")])]}}])})],1),t._v(" "),t.hasSearched?i("div",{staticClass:"pagination-container",staticStyle:{"margin-top":"20px","text-align":"center"}},[i("el-button",{attrs:{disabled:!t.hasPrevPage,size:"small"},on:{click:t.handlePrevPage}},[t._v(t._s(t.$t("affiliateProducts.prevPage")))]),t._v(" "),i("el-button",{attrs:{disabled:!t.hasNextPage,size:"small"},on:{click:t.handleNextPage}},[t._v(t._s(t.$t("affiliateProducts.nextPage")))]),t._v(" "),i("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n        "+t._s(t.$t("affiliateProducts.pageSize"))+"\n        "),i("el-select",{staticStyle:{width:"80px"},attrs:{size:"mini"},on:{change:t.handleSearch},model:{value:t.searchForm.pageSize,callback:function(e){t.$set(t.searchForm,"pageSize",e)},expression:"searchForm.pageSize"}},[i("el-option",{attrs:{label:"5",value:5}}),t._v(" "),i("el-option",{attrs:{label:"10",value:10}}),t._v(" "),i("el-option",{attrs:{label:"15",value:15}}),t._v(" "),i("el-option",{attrs:{label:"20",value:20}})],1)],1),t._v(" "),t.totalCount>0?i("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n        "+t._s(t.$t("affiliateProducts.totalCount",{count:t.totalCount}))+"\n      ")]):t._e()],1):t._e()],1),t._v(" "),i("el-dialog",{attrs:{title:t.currentImportProduct?t.$t("affiliateProducts.importSingle"):t.$t("affiliateProducts.importBatch"),visible:t.importDialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(e){t.importDialogVisible=e}}},[t.currentImportProduct?i("div",[i("p",[i("strong",[t._v(t._s(t.$t("affiliateProducts.productTitle"))+"：")]),t._v(t._s(t.currentImportProduct.title))]),t._v(" "),i("p",[i("strong",[t._v(t._s(t.$t("product.productId"))+"：")]),t._v(t._s(t.currentImportProduct.id))])]):i("div",[i("p",[i("strong",[t._v(t._s(t.$t("affiliateProducts.selectedCount")))]),t._v(t._s(t.selectedProducts.length))])]),t._v(" "),i("div",{staticStyle:{margin:"20px 0",padding:"15px","background-color":"#f0f9ff",border:"1px solid #b3d8ff","border-radius":"4px"}},[i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eff","margin-right":"8px"}}),t._v(" "),i("span",{staticStyle:{color:"#409eff"}},[t._v(t._s(t.$t("affiliateProducts.brandAutoDetect")))])]),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.importDialogVisible=!1}}},[t._v(t._s(t.$t("affiliateProducts.cancel")))]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.confirmImport}},[t._v(t._s(t.$t("affiliateProducts.confirmImport")))])],1)])],1)},o=[],r=i("b775");function a(t){return Object(r["a"])({url:"/admin/affiliate/products/search",method:"post",data:t})}function n(t){return Object(r["a"])({url:"/admin/affiliate/products/import",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/admin/affiliate/products/delete",method:"post",data:t})}var l={name:"AffiliateProducts",data:function(){return{loading:!1,tableData:[],searchForm:{keywords:[],priceMin:"",priceMax:"",commissionMin:"",commissionMax:"",sortField:"commission_rate",sortOrder:"DESC",pageSize:5},searchRules:{},currentCursor:"",nextPageToken:"",prevPageTokens:[],totalCount:0,hasNextPage:!1,hasPrevPage:!1,hasSearched:!1,selectedProducts:[],batchImporting:!1,batchDeleting:!1,importDialogVisible:!1,currentImportProduct:null,commissionErrors:{commissionMin:"",commissionMax:""},currentKeyword:"",keywordError:""}},mounted:function(){},methods:{validateCommissionRate:function(t){var e=this.searchForm[t];if(""===e||null===e||void 0===e)return this.commissionErrors[t]="",!0;var i=parseFloat(e);return isNaN(i)?(this.commissionErrors[t]=this.$t("affiliateProducts.commissionInvalidNumber"),!1):0!==i&&i<1e3?(this.commissionErrors[t]=this.$t("affiliateProducts.commissionRangeError"),!1):(this.commissionErrors[t]="",!0)},clearCommissionError:function(t){this.commissionErrors[t]=""},addKeyword:function(){var t=this.currentKeyword.trim();t?this.searchForm.keywords.length>=20?this.keywordError=this.$t("affiliateProducts.keywordLimitExceeded"):t.length>255?this.keywordError=this.$t("affiliateProducts.keywordTooLong"):this.searchForm.keywords.includes(t)?this.keywordError=this.$t("affiliateProducts.keywordDuplicate"):(this.searchForm.keywords.push(t),this.currentKeyword="",this.keywordError=""):this.keywordError=""},removeKeyword:function(t){this.searchForm.keywords.splice(t,1),this.keywordError=""},clearAllKeywords:function(){var t=this;this.$confirm(this.$t("affiliateProducts.confirmClearAllKeywords"),this.$t("affiliateProducts.clearAllKeywords"),{confirmButtonText:this.$t("common.confirm"),cancelButtonText:this.$t("common.cancel"),type:"warning"}).then((function(){t.searchForm.keywords=[],t.keywordError="",t.$message.success(t.$t("affiliateProducts.keywordsClearedSuccess"))})).catch((function(){}))},handleSearch:function(){var t=this.validateCommissionRate("commissionMin"),e=this.validateCommissionRate("commissionMax");t&&e?(this.currentCursor="",this.nextPageToken="",this.prevPageTokens=[],this.hasPrevPage=!1,this.hasSearched=!0,this.loadData()):this.$message.error(this.$t("affiliateProducts.pleaseFixErrors"))},handleReset:function(){this.searchForm={keywords:[],priceMin:"",priceMax:"",commissionMin:"",commissionMax:"",sortField:"commission_rate",sortOrder:"DESC",pageSize:5},this.commissionErrors={commissionMin:"",commissionMax:""},this.currentKeyword="",this.keywordError="",this.tableData=[],this.hasSearched=!1,this.totalCount=0,this.hasNextPage=!1,this.hasPrevPage=!1,this.currentCursor="",this.nextPageToken="",this.prevPageTokens=[]},handleRefresh:function(){this.hasSearched?this.loadData():this.$message.warning(this.$t("affiliateProducts.searchFirst"))},handleNextPage:function(){this.hasNextPage&&this.nextPageToken&&(this.prevPageTokens.push(this.currentCursor),this.currentCursor=this.nextPageToken,this.hasPrevPage=!0,this.loadData())},handlePrevPage:function(){this.hasPrevPage&&this.prevPageTokens.length>0&&(this.currentCursor=this.prevPageTokens.pop(),0===this.prevPageTokens.length&&(this.hasPrevPage=!1),this.loadData())},loadData:function(){var t=this;this.loading=!0;var e={pageSize:this.searchForm.pageSize,cursor:this.currentCursor,sortField:this.searchForm.sortField,sortOrder:this.searchForm.sortOrder,titleKeywords:this.searchForm.keywords.length>0?this.searchForm.keywords:null,salesPriceMin:this.searchForm.priceMin?parseFloat(this.searchForm.priceMin):null,salesPriceMax:this.searchForm.priceMax?parseFloat(this.searchForm.priceMax):null,commissionRateMin:this.searchForm.commissionMin?parseFloat(this.searchForm.commissionMin):null,commissionRateMax:this.searchForm.commissionMax?parseFloat(this.searchForm.commissionMax):null};a(e).then((function(e){t.tableData=e.products||[],t.nextPageToken=e.nextPageToken||"",t.totalCount=e.totalCount||0,t.hasNextPage=!!t.nextPageToken,0===t.tableData.length&&t.$message.info(t.$t("affiliateProducts.noResults"))})).catch((function(){t.loading=!1})).finally((function(){t.loading=!1}))},formatPrice:function(t){if(!t)return"-";var e=t.minimumAmount,i=t.maximumAmount,s=t.currency;return e===i?"".concat(e," ").concat(s):"".concat(e," - ").concat(i," ").concat(s)},formatCommissionRate:function(t){return t?(t/100).toFixed(2):"0"},handleSelectionChange:function(t){this.selectedProducts=t},handleImportProduct:function(t){this.currentImportProduct=t,this.importDialogVisible=!0},handleDeleteProduct:function(t){var e=this;this.$confirm(this.$t("affiliateProducts.deleteConfirm"),this.$t("common.deleteConfirm"),{confirmButtonText:this.$t("common.confirm"),cancelButtonText:this.$t("common.cancel"),type:"warning"}).then((function(){t.deleting=!0,c([t.id]).then((function(){e.$message.success(e.$t("affiliateProducts.deleteSuccess"))})).catch((function(){})).finally((function(){t.deleting=!1}))})).catch((function(){}))},handleBatchImport:function(){0!==this.selectedProducts.length?(this.currentImportProduct=null,this.importDialogVisible=!0):this.$message.warning(this.$t("affiliateProducts.selectFirst"))},handleBatchDelete:function(){var t=this;0!==this.selectedProducts.length?this.$confirm(this.$t("affiliateProducts.batchDeleteConfirm",{count:this.selectedProducts.length}),this.$t("affiliateProducts.batchDelete"),{confirmButtonText:this.$t("common.confirm"),cancelButtonText:this.$t("common.cancel"),type:"warning"}).then((function(){t.batchDeleting=!0;var e=t.selectedProducts.map((function(t){return t.id}));c(e).then((function(){t.$message.success(t.$t("affiliateProducts.batchDeleteSuccess")),t.$refs.productTable.clearSelection()})).catch((function(){})).finally((function(){t.batchDeleting=!1}))})).catch((function(){})):this.$message.warning(this.$t("affiliateProducts.selectDeleteFirst"))},confirmImport:function(){var t=this;if(this.currentImportProduct)this.currentImportProduct.importing=!0,this.importDialogVisible=!1,n({productIds:[this.currentImportProduct.id],operationUser:this.$store.getters.name||"admin"}).then((function(e){0===e.failedCount?e.skippedCount>0?t.$message.warning(t.$t("affiliateProducts.importExists")):t.$message.success(t.$t("affiliateProducts.importSuccess")):t.$message.error(t.$t("affiliateProducts.importFailed",{reason:e.failedProducts&&e.failedProducts.length>0?e.failedProducts[0].errorMessage:t.$t("common.unknownError")}))})).catch((function(){})).finally((function(){t.currentImportProduct.importing=!1}));else{this.batchImporting=!0,this.importDialogVisible=!1;var e=this.selectedProducts.map((function(t){return t.id}));n({productIds:e,operationUser:this.$store.getters.name||"admin"}).then((function(e){0===e.failedCount?e.skippedCount>0&&0===e.successCount?t.$message.warning(t.$t("affiliateProducts.batchImportExists")):e.skippedCount>0?t.$message.success(t.$t("affiliateProducts.batchImportMixed",{success:e.successCount,skipped:e.skippedCount})):t.$message.success(t.$t("affiliateProducts.batchImportSuccess",{count:e.successCount})):e.successCount>0?t.$message.warning(t.$t("affiliateProducts.batchImportPartial",{success:e.successCount,failed:e.failedCount,skipped:e.skippedCount})):t.$message.error(t.$t("affiliateProducts.batchImportFailed",{failed:e.failedCount,skipped:e.skippedCount})),t.$refs.productTable.clearSelection()})).catch((function(){})).finally((function(){t.batchImporting=!1}))}}}},d=l,u=(i("a58b"),i("2877")),m=Object(u["a"])(d,s,o,!1,null,"365864ff",null);e["default"]=m.exports}}]);