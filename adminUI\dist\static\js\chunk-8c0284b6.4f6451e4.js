(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8c0284b6"],{"0818":function(t,e,a){"use strict";a("fd61")},"2f2c":function(t,e,a){"use strict";a.d(e,"b",(function(){return u})),a.d(e,"c",(function(){return m})),a.d(e,"r",(function(){return d})),a.d(e,"d",(function(){return f})),a.d(e,"a",(function(){return p})),a.d(e,"g",(function(){return h})),a.d(e,"h",(function(){return b})),a.d(e,"j",(function(){return g})),a.d(e,"i",(function(){return v})),a.d(e,"e",(function(){return y})),a.d(e,"o",(function(){return w})),a.d(e,"q",(function(){return _})),a.d(e,"l",(function(){return V})),a.d(e,"m",(function(){return k})),a.d(e,"n",(function(){return x})),a.d(e,"p",(function(){return C})),a.d(e,"k",(function(){return O})),a.d(e,"f",(function(){return j}));var r=a("b775");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function o(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(Object(a),!0).forEach((function(e){s(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function s(t,e,a){return(e=l(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function l(t){var e=c(t,"string");return"symbol"==i(e)?e:e+""}function c(t,e){if("object"!=i(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(r["a"])({url:"/admin/system/city/list",method:"get",params:o({},t)})}function m(){return Object(r["a"])({url:"/admin/system/city/list/tree",method:"get"})}function d(t){return Object(r["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},t)})}function f(t){return Object(r["a"])({url:"/admin/system/city/update",method:"post",params:o({},t)})}function p(t){return Object(r["a"])({url:"/admin/system/city/info",method:"get",params:o({},t)})}function h(t){return Object(r["a"])({url:"/admin/express/list",method:"get",params:o({},t)})}function b(){return Object(r["a"])({url:"/admin/express/sync/express",method:"post"})}function g(t){return Object(r["a"])({url:"/admin/express/update/show",method:"post",data:t})}function v(t){return Object(r["a"])({url:"/admin/express/update",method:"post",data:t})}function y(t){return Object(r["a"])({url:"/admin/express/delete",method:"GET",params:o({},t)})}function w(t){return Object(r["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},t)})}function _(t){return Object(r["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},t)})}function V(t){return Object(r["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},t)})}function k(t){return Object(r["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},t)})}function x(t){return Object(r["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function C(t,e){return Object(r["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:o({},e)})}function O(t){return Object(r["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function j(t){return Object(r["a"])({url:"admin/express/info",method:"get",params:o({},t)})}},"31e1":function(t,e,a){},6065:function(t,e,a){"use strict";a("31e1")},bb50:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t.checkPermi(["admin:product:tabs:headers"])?a("el-tabs",{on:{"tab-click":t.seachList},model:{value:t.tableFrom.type,callback:function(e){t.$set(t.tableFrom,"type",e)},expression:"tableFrom.type"}},t._l(t.headeNum,(function(t,e){return a("el-tab-pane",{key:e,attrs:{label:t.name+"("+t.count+")",name:t.type.toString()}})})),1):t._e(),t._v(" "),a("div",{staticClass:"container mt-1"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-form-item",{attrs:{label:"商品分类："}},[a("el-cascader",{staticClass:"selWidth mr20",attrs:{options:t.merCateList,props:t.props,clearable:"",size:"small"},on:{change:t.seachList},model:{value:t.tableFrom.cateId,callback:function(e){t.$set(t.tableFrom,"cateId",e)},expression:"tableFrom.cateId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称，关键字，商品ID",size:"small",clearable:""},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:list"],expression:"['admin:product:list']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1),t._v(" "),a("router-link",{attrs:{to:{path:"/store/list/creatProduct"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:save"],expression:"['admin:product:save']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"}},[t._v("添加商品")])],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:save"],expression:"['admin:product:save']"}],staticClass:"mr10",attrs:{size:"small",type:"success"},on:{click:t.onCopy}},[t._v("商品采集")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:product"],expression:"['admin:export:excel:product']"}],attrs:{size:"small",icon:"el-icon-upload2"},on:{click:t.exports}},[t._v("导出")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"商品分类："}},t._l(e.row.cateValues.split(","),(function(e,r){return a("span",{key:r,staticClass:"mr10"},[t._v(t._s(e))])})),0),t._v(" "),a("el-form-item",{attrs:{label:"市场价："}},[a("span",[t._v(t._s(e.row.otPrice))])]),t._v(" "),a("el-form-item",{attrs:{label:"成本价："}},[a("span",[t._v(t._s(e.row.cost))])]),t._v(" "),a("el-form-item",{attrs:{label:"收藏："}},[a("span",[t._v(t._s(e.row.collectCount))])]),t._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("span",[t._v(t._s(e.row.ficti))])])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"商品图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品名称",prop:"storeName","min-width":"300","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"price",label:"商品售价","min-width":"90",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"90",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"90",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序","min-width":"70",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"添加时间","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatDate")(e.row.addTime)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["admin:product:up","admin:product:down"])?[a("el-switch",{attrs:{disabled:Number(t.tableFrom.type)>2,"active-value":!0,"inactive-value":!1,"active-text":"上架","inactive-text":"下架"},on:{change:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.isShow,callback:function(a){t.$set(e.row,"isShow",a)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{attrs:{to:{path:"/store/list/creatProduct/"+e.row.id+"/1"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:info"],expression:"['admin:product:info']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[t._v("详情")])],1),t._v(" "),a("router-link",{attrs:{to:{path:"/store/list/creatProduct/"+e.row.id}}},["5"!==t.tableFrom.type&&"1"!==t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:update"],expression:"['admin:product:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[t._v("编辑")]):t._e()],1),t._v(" "),"5"===t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:restore"],expression:"['admin:product:restore']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleRestore(e.row.id,e.$index)}}},[t._v("恢复商品")]):t._e(),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:delete"],expression:"['admin:product:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,t.tableFrom.type)}}},[t._v(t._s("5"===t.tableFrom.type?"删除":"加入回收站"))])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-dialog",{staticClass:"taoBaoModal",attrs:{title:"复制淘宝、天猫、京东、苏宁",visible:t.dialogVisible,"close-on-click-modal":!1,width:"1200px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?a("tao-bao",{on:{handleCloseMod:t.handleCloseMod}}):t._e()],1)],1)},i=[],n=a("73f5"),o=(a("5f87"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"Box"},[a("el-card",[a("div",{staticClass:"line-ht"},[t._v("生成的商品默认是没有上架的，请手动上架商品！\n      "),t.copyConfig.copyType&&1==t.copyConfig.copyType?a("span",[t._v("您当前剩余"+t._s(t.copyConfig.copyNum)+"条采集次数，\n        "),a("router-link",{attrs:{to:{path:"/operation/systemSms/pay?type=copy"}}},[a("span",{staticStyle:{color:"#1890ff"}},[t._v("增加采集次数")])])],1):t._e(),t._v(" "),t.copyConfig.copyType&&1!=t.copyConfig.copyType?a("el-link",{attrs:{type:"primary",underline:!1,href:"https://help.crmeb.net/crmeb_java/2103903",target:"_blank"}},[t._v("如何配置密钥\n      ")]):t._e(),t._v(" "),a("br"),t._v("\n      商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置（如配置一号通采集，请先登录一号通账号，无一号通，请选择99Api设置）\n    ")],1)]),t._v(" "),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{model:t.formValidate,rules:t.ruleInline,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[t.copyConfig.copyType&&1!=t.copyConfig.copyType?a("el-form-item",[a("el-radio-group",{model:{value:t.form,callback:function(e){t.form=e},expression:"form"}},[a("el-radio",{attrs:{label:1}},[t._v("淘宝")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("京东")]),t._v(" "),a("el-radio",{attrs:{label:5}},[t._v("天猫")])],1)],1):t._e(),t._v(" "),a("el-row",{attrs:{gutter:24}},[t.copyConfig.copyType?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"链接地址："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入链接地址",size:"small"},model:{value:t.url,callback:function(e){t.url=e},expression:"url"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:copy:product","admin:product:import:product"],expression:"['admin:product:copy:product','admin:product:import:product']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.add},slot:"append"})],1)],1)],1):t._e(),t._v(" "),t.formValidate?a("el-col",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品名称：",prop:"storeName"}},[a("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称"},model:{value:t.formValidate.storeName,callback:function(e){t.$set(t.formValidate,"storeName",e)},expression:"formValidate.storeName"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品简介："}},[a("el-input",{attrs:{maxlength:"250",type:"textarea",rows:3,placeholder:"请输入商品简介"},model:{value:t.formValidate.storeInfo,callback:function(e){t.$set(t.formValidate,"storeInfo",e)},expression:"formValidate.storeInfo"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品分类：",prop:"cateIds"}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.merCateList,props:t.props2,clearable:"","show-all-levels":!1},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品关键字：",prop:"keyword"}},[a("el-input",{attrs:{placeholder:"请输入商品关键字"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入单位"},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品封面图：",prop:"image"}},[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.formValidate.image}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品轮播图："}},[a("div",{staticClass:"acea-row"},t._l(t.formValidate.sliderImages,(function(e,r){return a("div",{key:r,staticClass:"lunBox mr5",attrs:{draggable:"false"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:e}})]),t._v(" "),a("el-button-group",[a("el-button",{attrs:{size:"mini"},nativeOn:{click:function(a){return t.checked(e,r)}}},[t._v("主图")]),t._v(" "),a("el-button",{attrs:{size:"mini"},nativeOn:{click:function(e){return t.handleRemove(r)}}},[t._v("移除")])],1)],1)})),0)])],1),t._v(" "),t.formValidate.specType||t.formValidate.attr.length?a("el-col",{staticClass:"noForm",attrs:{span:24}},[a("el-form-item",{staticClass:"labeltop",attrs:{label:"批量设置："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.oneFormBatch,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","pi")}}},[e.row.image?a("div",{staticClass:"pictrue pictrueTab"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad pictrueTab"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3789294462)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{type:"商品编号"===t.formThead[r].title?"text":"number",min:0},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"}},[[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:t.batchAdd}},[t._v("批量添加")])]],2)],2)],1)],1):t._e(),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品规格：",props:"spec_type","label-for":"spec_type"}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.formValidate.attrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:t.manyTabTit[r].title,"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:[{required:!0,message:"请上传图片",trigger:"change"}],prop:"attrValue."+e.$index+".image"}},[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?a("div",{staticClass:"pictrue pictrueTab"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad pictrueTab"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])])]}}],null,!1,799156793)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:[{required:!0,message:"请输入"+t.formThead[r].title,trigger:"blur"}],prop:"商品编号"!==t.formThead[r].title?"attrValue."+e.$index+"."+r:""}},[a("el-input",{staticClass:"priceBox",attrs:{type:"商品编号"===t.formThead[r].title?"text":"number"},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})],1)]}}],null,!0)})})),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:function(a){return t.delAttrTable(e.$index)}}},[t._v("删除")])]}}],null,!1,2803824461)})],2)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("Tinymce",{model:{value:t.formValidate.content,callback:function(e){t.$set(t.formValidate,"content",e)},expression:"formValidate.content"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:24}},[[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"排序："}},[a("el-input-number",{attrs:{max:9999,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"积分："}},[a("el-input-number",{attrs:{placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.giveIntegral,callback:function(e){t.$set(t.formValidate,"giveIntegral",e)},expression:"formValidate.giveIntegral"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"虚拟销量："}},[a("el-input-number",{attrs:{placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1)],1)],1)]],2),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品推荐："}},[a("el-checkbox-group",{attrs:{size:"small",disabled:t.isDisabled},on:{change:t.onChangeGroup},model:{value:t.checkboxGroup,callback:function(e){t.checkboxGroup=e},expression:"checkboxGroup"}},t._l(t.recommend,(function(e,r){return a("el-checkbox",{key:r,attrs:{label:e.value}},[t._v(t._s(e.name))])})),1)],1)],1)],1)]],2),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",[a("el-button",{staticClass:"submission",attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交\n            ")])],1)],1)],1):t._e()],1)],1)],1)}),s=[],l=a("e7ac"),c=a("8256"),u=a("2f2c"),m=a("61f7");function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function f(t){return b(t)||h(t)||C(t)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function b(t){if(Array.isArray(t))return O(t)}function g(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function n(a,r,i,n){var l=r&&r.prototype instanceof s?r:s,c=Object.create(l.prototype);return v(c,"_invoke",function(a,r,i){var n,s,l,c=0,u=i||[],m=!1,d={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,a){return n=e,s=0,l=t,d.n=a,o}};function f(a,r){for(s=a,l=r,e=0;!m&&c&&!i&&e<u.length;e++){var i,n=u[e],f=d.p,p=n[2];a>3?(i=p===r)&&(l=n[(s=n[4])?5:(s=3,3)],n[4]=n[5]=t):n[0]<=f&&((i=a<2&&f<n[1])?(s=0,d.v=r,d.n=n[1]):f<p&&(i=a<3||n[0]>r||r>p)&&(n[4]=a,n[5]=r,d.n=p,s=0))}if(i||a>1)return o;throw m=!0,r}return function(i,u,p){if(c>1)throw TypeError("Generator is already running");for(m&&1===u&&f(u,p),s=u,l=p;(e=s<2?t:l)||!m;){n||(s?s<3?(s>1&&(d.n=-1),f(s,l)):d.n=l:d.v=l);try{if(c=2,n){if(s||(i="next"),e=n[i]){if(!(e=e.call(n,l)))throw TypeError("iterator result is not an object");if(!e.done)return e;l=e.value,s<2&&(s=0)}else 1===s&&(e=n.return)&&e.call(n),s<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),s=1);n=t}else if((e=(m=d.n<0)?l:a.call(r,d))!==o)break}catch(e){n=t,s=1,l=e}finally{c=1}}return{value:e,done:m}}}(a,i,n),!0),c}var o={};function s(){}function l(){}function c(){}e=Object.getPrototypeOf;var u=[][r]?e(e([][r]())):(v(e={},r,(function(){return this})),e),m=c.prototype=s.prototype=Object.create(u);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,c):(t.__proto__=c,v(t,i,"GeneratorFunction")),t.prototype=Object.create(m),t}return l.prototype=c,v(m,"constructor",c),v(c,"constructor",l),l.displayName="GeneratorFunction",v(c,i,"GeneratorFunction"),v(m),v(m,i,"Generator"),v(m,r,(function(){return this})),v(m,"toString",(function(){return"[object Generator]"})),(g=function(){return{w:n,m:d}})()}function v(t,e,a,r){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}v=function(t,e,a,r){function n(e,a){v(t,e,(function(t){return this._invoke(e,a,t)}))}e?i?i(t,e,{value:a,enumerable:!r,configurable:!r,writable:!r}):t[e]=a:(n("next",0),n("throw",1),n("return",2))},v(t,e,a,r)}function y(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(t){return void a(t)}s.done?e(l):Promise.resolve(l).then(r,i)}function w(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function o(t){y(n,r,i,o,s,"next",t)}function s(t){y(n,r,i,o,s,"throw",t)}o(void 0)}))}}function _(t,e,a){return(e=V(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function V(t){var e=k(t,"string");return"symbol"==d(e)?e:e+""}function k(t,e){if("object"!=d(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function x(t,e){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=C(t))||e&&t&&"number"==typeof t.length){a&&(t=a);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,o=!0,s=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return o=t.done,t},e:function(t){s=!0,n=t},f:function(){try{o||null==a.return||a.return()}finally{if(s)throw n}}}}function C(t,e){if(t){if("string"==typeof t)return O(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?O(t,e):void 0}}function O(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=Array(e);a<e;a++)r[a]=t[a];return r}var j=[{image:"",price:null,cost:null,otPrice:null,stock:null,barCode:"",weight:0,volume:0}],S={price:{title:"售价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},I={name:"taoBao",components:{Tinymce:c["a"]},data:function(){return{loading:!1,formThead:Object.assign({},S),manyTabTit:{},manyTabDate:{},formValidate:null,form:1,props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},checkboxGroup:[],recommend:[],modal_loading:!1,ManyAttrValue:[Object.assign({},j[0])],imgList:[],tempData:{page:1,limit:9999},shippingList:[],merCateList:[],images:"",url:"",modalPic:!1,isChoice:"",isDisabled:!1,ruleInline:{storeName:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cateIds:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change",type:"number"}],keyword:[{required:!0,message:"请输入商品关键字",trigger:"blur"}],attrValue:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}]},grid:{xl:12,lg:12,md:12,sm:24,xs:24},copyConfig:{}}},created:function(){this.goodsCategory()},computed:{attrValue:function(){var t=Object.assign({},j[0]);return delete t.image,t},oneFormBatch:function(){var t=[Object.assign({},j[0])];return delete t[0].barCode,t}},watch:{"formValidate.attr":{handler:function(t){this.watCh(t)},immediate:!1,deep:!0}},mounted:function(){this.productGetTemplate(),this.getCopyConfig(),this.getGoodsType()},methods:{delAttrTable:function(t){this.formValidate.attrValue.splice(t,1)},getCopyConfig:function(){var t=this;Object(n["e"])().then((function(e){t.copyConfig=e}))},onChangeGroup:function(){this.checkboxGroup.includes("isGood")?this.formValidate.isGood=!0:this.formValidate.isGood=!1,this.checkboxGroup.includes("isBenefit")?this.formValidate.isBenefit=!0:this.formValidate.isBenefit=!1,this.checkboxGroup.includes("isBest")?this.formValidate.isBest=!0:this.formValidate.isBest=!1,this.checkboxGroup.includes("isNew")?this.formValidate.isNew=!0:this.formValidate.isNew=!1,this.checkboxGroup.includes("isHot")?this.formValidate.isHot=!0:this.formValidate.isHot=!1},batchAdd:function(){var t,e=x(this.formValidate.attrValue);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.$set(a,"image",this.oneFormBatch[0].image),this.$set(a,"price",this.oneFormBatch[0].price),this.$set(a,"cost",this.oneFormBatch[0].cost),this.$set(a,"otPrice",this.oneFormBatch[0].otPrice),this.$set(a,"stock",this.oneFormBatch[0].stock),this.$set(a,"barCode",this.oneFormBatch[0].barCode),this.$set(a,"weight",this.oneFormBatch[0].weight),this.$set(a,"volume",this.oneFormBatch[0].volume)}}catch(r){e.e(r)}finally{e.f()}},watCh:function(t){var e={},a={};this.formValidate.attr.forEach((function(t,r){e[t.attrName]={title:t.attrName},a[t.attrName]=""})),this.formValidate.attrValue=this.attrFormat(t),this.manyTabTit=e,this.manyTabDate=a,this.formThead=Object.assign({},this.formThead,e)},attrFormat:function(t){var e=[],a=[];return r(t);function r(t){if(t.length>1)t.forEach((function(r,i){0===i&&(e=t[i]["attrValue"]);var n=[];e.forEach((function(e){t[i+1]&&t[i+1]["attrValue"]&&t[i+1]["attrValue"].forEach((function(r){var o=(0!==i?"":t[i]["attrName"]+"_")+e+"$&"+t[i+1]["attrName"]+"_"+r;if(n.push(o),i===t.length-2){var s={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0};for(var l in o.split("$&").forEach((function(t,e){var a=t.split("_");s["attrValue"]||(s["attrValue"]={}),s["attrValue"][a[0]]=a.length>1?a[1]:""})),s.attrValue)s[l]=s.attrValue[l];a.push(s)}}))})),e=n.length?n:[]}));else{var r=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,i){for(var n in r[i]=t["attrName"]+"_"+e,a[i]={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0,attrValue:_({},t["attrName"],e)},a[i].attrValue)a[i][n]=a[i].attrValue[n]}))})),e.push(r.join("$&"))}return a}},productGetTemplate:function(){var t=this;Object(u["o"])(this.tempData).then((function(e){t.shippingList=e.list}))},handleRemove:function(t){this.formValidate.sliderImages.splice(t,1),this.$forceUpdate()},checked:function(t,e){this.formValidate.image=t},goodsCategory:function(){var t=this;Object(n["d"])({status:-1,type:1}).then((function(e){t.merCateList=e}))},add:function(){var t=this;this.url?(this.loading=!0,1==this.copyConfig.copyType?Object(n["f"])({url:this.url}).then((function(e){var a=e.info;t.formValidate={image:t.$selfUtil.setDomain(a.image),sliderImage:a.sliderImage,storeName:a.storeName,storeInfo:a.storeInfo,keyword:a.keyword,cateIds:a.cateId?a.cateId.split(","):[],cateId:a.cateId,unitName:a.unitName,sort:0,isShow:0,isBenefit:0,isNew:0,isGood:0,isHot:0,isBest:0,tempId:a.tempId,attrValue:a.attrValue,attr:a.attr||[],selectRule:a.selectRule,isSub:!1,content:t.$selfUtil.replaceImgSrcHttps(a.content),specType:!!a.attr.length,id:a.id,giveIntegral:a.giveIntegral,ficti:a.ficti},a.isHot&&t.checkboxGroup.push("isHot"),a.isGood&&t.checkboxGroup.push("isGood"),a.isBenefit&&t.checkboxGroup.push("isBenefit"),a.isBest&&t.checkboxGroup.push("isBest"),a.isNew&&t.checkboxGroup.push("isNew");var r=JSON.parse(a.sliderImage),i=[];if(Object.keys(r).map((function(e){i.push(t.$selfUtil.setDomain(r[e]))})),t.formValidate.sliderImages=i,t.formValidate.attr.length){t.oneFormBatch[0].image=t.$selfUtil.setDomain(a.image);for(var n=0;n<t.formValidate.attr.length;n++)t.formValidate.attr[n].attrValue=JSON.parse(t.formValidate.attr[n].attrValues)}t.loading=!1})).catch((function(){t.loading=!1})):Object(n["g"])({url:this.url,form:this.form}).then((function(e){t.formValidate={image:t.$selfUtil.setDomain(e.image),sliderImage:e.sliderImage,storeName:e.storeName,storeInfo:e.storeInfo,keyword:e.keyword,cateIds:e.cateId?e.cateId.split(","):[],cateId:e.cateId,unitName:e.unitName,sort:0,isShow:0,isBenefit:0,isNew:0,isGood:0,isHot:0,isBest:0,tempId:e.tempId,attrValue:e.attrValue,attr:e.attr||[],selectRule:e.selectRule,isSub:!1,content:e.content,specType:!!e.attr.length,id:e.id,giveIntegral:e.giveIntegral,ficti:e.ficti};var a=JSON.parse(e.sliderImage),r=[];if(Object.keys(a).map((function(e){r.push(t.$selfUtil.setDomain(a[e]))})),t.formValidate.sliderImages=r,t.formValidate.attr.length){t.oneFormBatch[0].image=t.$selfUtil.setDomain(e.image);for(var i=0;i<t.formValidate.attr.length;i++)t.formValidate.attr[i].attrValue=JSON.parse(t.formValidate.attr[i].attrValues)}t.loading=!1})).catch((function(){t.loading=!1}))):this.$message.warning("请输入链接地址！")},handleSubmit:Object(m["a"])((function(t){var e=this,a=JSON.parse(JSON.stringify(this.formValidate));a.attr.forEach((function(t){t.attrValues=t.attrValue.join(",")})),a.cateId=a.cateIds.join(","),a.sliderImage=JSON.stringify(a.sliderImages),a.attrValue.forEach((function(t){t.attrValue=JSON.stringify(t.attrValue)})),this.$refs[t].validate((function(t){t?(e.modal_loading=!0,Object(n["j"])(a).then(function(){var t=w(g().m((function t(a){return g().w((function(t){while(1)switch(t.n){case 0:e.$message.success("新增成功"),e.$emit("handleCloseMod",!1),e.modal_loading=!1;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(){e.modal_loading=!1}))):a.storeName&&a.cateId&&a.keyword&&a.unitName&&a.image||e.$message.warning("请填写完整商品信息！")}))})),modalPicTap:function(t,e,a){var r=this;this.$modalUpload((function(i){if("1"!==t||e||(r.formValidate.image=i[0].sattDir,r.OneattrValue[0].image=i[0].sattDir),"2"===t&&!e){if(i.length>10)return this.$message.warning("最多选择10张图片！");if(i.length+r.formValidate.sliderImages.length>10)return this.$message.warning("最多选择10张图片！");i.map((function(t){r.formValidate.sliderImages.push(t.sattDir)}))}"1"===t&&"dan"===e&&(r.OneattrValue[0].image=i[0].sattDir),"1"===t&&"duo"===e&&(r.formValidate.attrValue[a].image=i[0].sattDir),"1"===t&&"pi"===e&&(r.oneFormBatch[0].image=i[0].sattDir)}),t,"store")},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var a=f(this.formValidate.slider_image),r=a.indexOf(this.dragging),i=a.indexOf(e);a.splice.apply(a,[i,0].concat(f(a.splice(r,1)))),this.formValidate.slider_image=a}},getGoodsType:function(){var t=this;Object(l["a"])({gid:70}).then((function(e){var a=e.list,r=[],i=[],n=[{name:"是否热卖",value:"isHot"}],o=[{name:"",value:"isGood",type:"2"},{name:"",value:"isBenefit",type:"4"},{name:"",value:"isBest",type:"1"},{name:"",value:"isNew",type:"3"}];a.forEach((function(t){var e={};e.value=JSON.parse(t.value),e.id=t.id,e.gid=t.gid,e.status=t.status,r.push(e)})),r.forEach((function(t){var e={};e.name=t.value.fields[1].value,e.status=t.status,e.type=t.value.fields[3].value,i.push(e)})),o.forEach((function(t){i.forEach((function(e){t.type==e.type&&n.push({name:e.name,value:t.value,type:t.type})}))})),t.recommend=n}))}}},N=I,P=(a("0818"),a("2877")),T=Object(P["a"])(N,o,s,!1,null,"a28b6d5c",null),$=T.exports,D=a("e350"),F={name:"ProductList",components:{taoBao:$},data:function(){return{props:{children:"child",label:"name",value:"id",emitPath:!1},headeNum:[],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,cateId:"",keywords:"",type:"1"},categoryList:[],merCateList:[],objectUrl:"https://adminapi.genconusantara.com",dialogVisible:!1}},mounted:function(){this.goodHeade(),this.getList(),this.getCategorySelect(),this.checkedCities=this.$cache.local.has("goods_stroge")?this.$cache.local.getJSON("goods_stroge"):this.checkedCities},methods:{checkPermi:D["a"],handleRestore:function(t){var e=this;this.$modalSure("恢复商品").then((function(){Object(n["w"])(t).then((function(t){e.$message.success("操作成功"),e.goodHeade(),e.getList()}))}))},seachList:function(){this.tableFrom.page=1,this.getList()},handleClose:function(){this.dialogVisible=!1},handleCloseMod:function(t){this.dialogVisible=t,this.goodHeade(),this.getList()},onCopy:function(){this.dialogVisible=!0},exports:function(){Object(n["m"])({cateId:this.tableFrom.cateId,keywords:this.tableFrom.keywords,type:this.tableFrom.type}).then((function(t){window.location.href=t.fileName}))},goodHeade:function(){var t=this;Object(n["n"])().then((function(e){t.headeNum=e})).catch((function(e){t.$message.error(e.message)}))},getCategorySelect:function(){var t=this;Object(n["d"])({status:-1,type:1}).then((function(e){t.merCateList=e})).catch((function(e){t.$message.error(e.message)}))},getList:function(){var t=this;this.listLoading=!0,Object(n["o"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e.message)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},handleDelete:function(t,e){var a=this;this.$modalSure("删除 id 为 ".concat(t," 的商品")).then((function(){var r=5==e?"delete":"recycle";Object(n["k"])(t,r).then((function(){a.$message.success("删除成功"),a.getList(),a.goodHeade()}))}))},onchangeIsShow:function(t){var e=this;t.isShow?Object(n["q"])(t.id).then((function(){e.$message.success("上架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow})):Object(n["h"])(t.id).then((function(){e.$message.success("下架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow}))}}},B=F,G=(a("6065"),Object(P["a"])(B,r,i,!1,null,"31f253bb",null));e["default"]=G.exports},e7ac:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return l}));var r=a("b775");function i(t){var e={id:t.id};return Object(r["a"])({url:"/admin/system/group/delete",method:"GET",params:e})}function n(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(r["a"])({url:"/admin/system/group/list",method:"GET",params:e})}function o(t){var e={formId:t.formId,info:t.info,name:t.name};return Object(r["a"])({url:"/admin/system/group/save",method:"POST",params:e})}function s(t){var e={formId:t.formId,info:t.info,name:t.name,id:t.id};return Object(r["a"])({url:"/admin/system/group/update",method:"POST",params:e})}function l(t){var e={gid:t.gid};return Object(r["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}},fd61:function(t,e,a){}}]);