(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-af2261d0"],{"2eb3":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return r})),n.d(t,"l",(function(){return l})),n.d(t,"k",(function(){return m})),n.d(t,"i",(function(){return u})),n.d(t,"f",(function(){return c})),n.d(t,"g",(function(){return p})),n.d(t,"h",(function(){return d})),n.d(t,"j",(function(){return f}));var s=n("b775");function o(e){var t={id:e.id};return Object(s["a"])({url:"/admin/system/admin/delete",method:"GET",params:t})}function i(e){return Object(s["a"])({url:"/admin/system/admin/list",method:"GET",params:e})}function a(e){var t={account:e.account,level:e.level,pwd:e.pwd,realName:e.realName,roles:e.roles.join(","),status:e.status,phone:e.phone};return Object(s["a"])({url:"/admin/system/admin/save",method:"POST",data:t})}function r(e){var t={account:e.account,level:e.level,pwd:e.pwd,roles:e.roles,realName:e.realName,status:e.status,id:e.id,isDel:e.isDel};return Object(s["a"])({url:"/admin/system/admin/update",method:"POST",data:t})}function l(e){return Object(s["a"])({url:"/admin/system/admin/updateStatus",method:"get",params:e})}function m(e){return Object(s["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:e})}function u(e){var t={menuType:e.menuType,name:e.name};return Object(s["a"])({url:"/admin/system/menu/list",method:"get",params:t})}function c(e){var t=e;return Object(s["a"])({url:"/admin/system/menu/add",method:"post",data:t})}function p(e){return Object(s["a"])({url:"/admin/system/menu/delete/".concat(e),method:"post"})}function d(e){return Object(s["a"])({url:"/admin/system/menu/info/".concat(e),method:"get"})}function f(e){var t=e;return Object(s["a"])({url:"/admin/system/menu/update",method:"post",data:t})}},"54f4":function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"divBox"},[n("el-card",{staticClass:"box-card"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0}},[n("el-form-item",{attrs:{label:e.$t("permissionRules.menuName"),prop:"menuName"}},[n("el-input",{attrs:{placeholder:e.$t("permissionRules.form.enterMenuName"),clearable:"",size:"small"},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("permissionRules.status"),prop:"menuType"}},[n("el-select",{attrs:{placeholder:e.$t("permissionRules.select"),clearable:"",size:"small"},model:{value:e.queryParams.menuType,callback:function(t){e.$set(e.queryParams,"menuType",t)},expression:"queryParams.menuType"}},e._l(e.statusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("common.query")))]),e._v(" "),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("common.reset")))])],1)],1),e._v(" "),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v(e._s(e.$t("permissionRules.actions.add")))])],1),e._v(" "),n("el-col",{attrs:{span:1.5}},[n("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-sort",size:"mini"},on:{click:e.toggleExpandAll}},[e._v(e._s(e.$t("permissionRules.expandCollapse")))])],1)],1),e._v(" "),e.refreshTable?n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{data:e.menuList,"row-key":"id","default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"header-cell-style":{fontWeight:"bold"}}},[n("el-table-column",{attrs:{label:e.$t("permissionRules.table.menuName"),"show-overflow-tooltip":!0,width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e.$t("dashboard."+t.row.name)?e.$t("dashboard."+t.row.name):t.row.name)+"\n        ")]}}],null,!1,2614828991)}),e._v(" "),n("el-table-column",{attrs:{prop:"icon",label:e.$t("permissionRules.table.icon"),align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[n("i",{class:"el-icon-"+e.row.icon,staticStyle:{"font-size":"20px"}})]}}],null,!1,244404100)}),e._v(" "),n("el-table-column",{attrs:{prop:"sort",label:e.$t("permissionRules.table.sort"),width:"60"}}),e._v(" "),n("el-table-column",{attrs:{prop:"perms",label:e.$t("permissionRules.table.perm"),"show-overflow-tooltip":!0}}),e._v(" "),n("el-table-column",{attrs:{prop:"component",label:e.$t("permissionRules.table.component"),"show-overflow-tooltip":!0}}),e._v(" "),n("el-table-column",{attrs:{prop:"isShow",label:e.$t("permissionRules.table.status"),width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:t.row.isShow?"":"danger"}},[e._v(e._s(t.row.isShow?e.$t("common.show"):e.$t("common.hide")))])]}}],null,!1,1720598096)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("permissionRules.table.createTime"),align:"center",prop:"createTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("permissionRules.table.type"),width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return["M"==t.row.menuType?n("span",{staticClass:"type_tag one"},[e._v(e._s(e.$t("permissionRules.menuType.directory")))]):"C"==t.row.menuType?n("span",{staticClass:"type_tag two"},[e._v(e._s(e.$t("permissionRules.menuType.menu")))]):n("span",{staticClass:"type_tag three",attrs:{type:"info"}},[e._v(e._s(e.$t("permissionRules.menuType.button")))])]}}],null,!1,3747608705)}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("common.actions"),align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:menu:info"],expression:"['admin:system:menu:info']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("permissionRules.actions.edit")))]),e._v(" "),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:menu:add"],expression:"['admin:system:menu:add']"}],attrs:{size:"mini",type:"text",icon:"el-icon-plus"},on:{click:function(n){return e.handleAdd(t.row)}}},[e._v(e._s(e.$t("permissionRules.actions.add")))]),e._v(" "),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:menu:delete"],expression:"['admin:system:menu:delete']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("permissionRules.actions.delete")))])]}}],null,!1,2405108024)})],1):e._e(),e._v(" "),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"680px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:e.$t("permissionRules.form.parentMenu")}},[n("treeselect",{attrs:{options:e.menuOptions,normalizer:e.normalizer,"show-count":!0,placeholder:e.$t("permissionRules.form.selectParentMenu")},model:{value:e.form.pid,callback:function(t){e.$set(e.form,"pid",t)},expression:"form.pid"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:e.$t("permissionRules.form.menuType"),prop:"menuType"}},[n("el-radio-group",{model:{value:e.form.menuType,callback:function(t){e.$set(e.form,"menuType",t)},expression:"form.menuType"}},[n("el-radio",{attrs:{label:"M"}},[e._v(e._s(e.$t("permissionRules.menuType.directory")))]),e._v(" "),n("el-radio",{attrs:{label:"C"}},[e._v(e._s(e.$t("permissionRules.menuType.menu")))]),e._v(" "),n("el-radio",{attrs:{label:"A"}},[e._v(e._s(e.$t("permissionRules.menuType.button")))])],1)],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},["A"!=e.form.menuType?n("el-form-item",{attrs:{label:e.$t("permissionRules.form.menuIcon")}},[n("el-form-item",[n("el-input",{attrs:{placeholder:e.$t("permissionRules.form.selectIcon")},model:{value:e.form.icon,callback:function(t){e.$set(e.form,"icon",t)},expression:"form.icon"}},[n("el-button",{attrs:{slot:"append",icon:"el-icon-circle-plus-outline"},on:{click:e.addIcon},slot:"append"})],1)],1)],1):e._e()],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("permissionRules.form.menuName"),prop:"menuName"}},[n("el-input",{attrs:{placeholder:e.$t("permissionRules.form.enterMenuName")},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("permissionRules.form.sort"),prop:"sort"}},[n("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),"A"!==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"component"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:e.$t("permissionRules.form.componentTip"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v("\n                "+e._s(e.$t("permissionRules.form.component"))+"\n              ")],1),e._v(" "),n("el-input",{attrs:{placeholder:e.$t("permissionRules.form.enterComponent")},model:{value:e.form.component,callback:function(t){e.$set(e.form,"component",t)},expression:"form.component"}})],1)],1):e._e(),e._v(" "),n("el-col",{attrs:{span:12}},["M"!=e.form.menuType?n("el-form-item",[n("el-input",{attrs:{placeholder:e.$t("permissionRules.form.enterPerm"),maxlength:"100"},model:{value:e.form.perms,callback:function(t){e.$set(e.form,"perms",t)},expression:"form.perms"}}),e._v(" "),n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:e.$t("permissionRules.form.permTip"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v("\n                "+e._s(e.$t("permissionRules.form.perm"))+"\n              ")],1)],1):e._e()],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:e.$t("permissionRules.form.showStatusTip"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v("\n                "+e._s(e.$t("permissionRules.form.showStatus"))+"\n              ")],1),e._v(" "),n("el-radio-group",{model:{value:e.form.isShow,callback:function(t){e.$set(e.form,"isShow",t)},expression:"form.isShow"}},e._l(e.showStatus,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1)],1),e._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:menu:update"],expression:"['admin:system:menu:update']"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("common.confirm")))]),e._v(" "),n("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("common.cancel")))])],1)],1)],1)],1)},o=[],i=n("2eb3"),a=n("ca17"),r=n.n(a),l=(n("542c"),n("61f7")),m={name:"Menu",components:{Treeselect:r.a},data:function(){return{listLoading:!0,showSearch:!0,menuList:[],menuOptions:[],title:"",open:!1,isExpandAll:!1,refreshTable:!0,queryParams:{name:"",menuType:""},form:{},menuDataList:[],rules:{name:[{required:!0,message:this.$t("permissionRules.form.enterMenuName"),trigger:"blur"}],sort:[{required:!0,message:this.$t("permissionRules.form.sortRequired"),trigger:"blur"}]},statusOptions:[{value:"M",label:this.$t("permissionRules.menuType.directory")},{value:"C",label:this.$t("permissionRules.menuType.menu")},{value:"A",label:this.$t("permissionRules.menuType.button")}],showStatus:[{label:this.$t("common.show"),value:!0},{label:this.$t("common.hide"),value:!1}]}},created:function(){this.getList()},methods:{addIcon:function(){var e=this;e.$modalIcon((function(t){e.form.icon=t}))},getList:function(){var e=this;this.listLoading=!0,Object(i["i"])(this.queryParams).then((function(t){var n={},s=[];t.forEach((function(e){n=e,n.parentId=e.pid,n.children=[],s.push(n)})),e.menuDataList=s,e.menuList=e.handleTree(s,"menuId"),e.listLoading=!1}))},normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.id?e.id:0,label:e.name?e.name:"主目录",children:e.children}},getTreeselect:function(){this.menuOptions=[];var e={menuId:0,menuName:"主类目",children:[]};e.children=this.handleTree(this.menuDataList,"menuId"),this.menuOptions.push(e)},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={menuId:"",parentId:0,name:"",icon:"",menuType:"M",sort:0,isShow:!0,component:"",perms:""},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.queryParams={name:"",menuType:""},this.handleQuery()},handleAdd:function(e){this.reset(),null!=e&&e.id?this.form.pid=e.id:this.form.pid=0,this.open=!0,this.title=this.$t("permissionRules.actions.add")},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this,n=this.$loading({lock:!0,text:"Loading"});this.reset(),this.getTreeselect(),Object(i["h"])(e.id).then((function(e){t.form=e,t.open=!0,t.title=t.$t("permissionRules.actions.edit"),n.close()}))},submitForm:Object(l["a"])((function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?Object(i["j"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("common.editSuccess")),e.open=!1,e.getList()})):Object(i["f"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("common.addSuccess")),e.open=!1,e.getList()})))}))})),handleDelete:function(e){var t=this;this.$modal.confirm(this.$t("common.confirmDelete").replace("{name}",e.name)).then((function(){return Object(i["g"])(e.id)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("common.deleteSuccess"))})).catch((function(){}))}}},u=m,c=(n("e28a"),n("2877")),p=Object(c["a"])(u,s,o,!1,null,null,null);t["default"]=p.exports},aa6e:function(e,t,n){},e28a:function(e,t,n){"use strict";n("aa6e")}}]);