(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6eacd0c7"],{"3bba":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox relative"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"container mt-1"},[r("el-form",{attrs:{inline:"",size:"small"},model:{value:t.searchFrom,callback:function(e){t.searchFrom=e},expression:"searchFrom"}},[r("el-form-item",{attrs:{label:t.$t("order.search.orderNo")+"："}},[r("el-input",{attrs:{placeholder:t.$t("order.search.orderNo")},model:{value:t.searchFrom.orderNo,callback:function(e){t.$set(t.searchFrom,"orderNo",e)},expression:"searchFrom.orderNo"}})],1),t._v(" "),r("el-form-item",{attrs:{label:t.$t("order.search.productTitle")+"："}},[r("el-input",{attrs:{placeholder:t.$t("order.search.productTitle")},model:{value:t.searchFrom.productTitle,callback:function(e){t.$set(t.searchFrom,"productTitle",e)},expression:"searchFrom.productTitle"}})],1),t._v(" "),r("el-form-item",{attrs:{label:t.$t("order.search.status")+"："}},[r("el-select",{attrs:{placeholder:t.$t("common.all")},model:{value:t.searchFrom.status,callback:function(e){t.$set(t.searchFrom,"status",e)},expression:"searchFrom.status"}},t._l(t.statusList,(function(e){return r("el-option",{key:e.value,attrs:{label:t.$t("order.search."+e.label),value:e.value}})})),1)],1)],1)],1),t._v(" "),r("el-button",{staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.getList(1)}}},[t._v("\n      "+t._s(t.$t("common.query"))+"\n    ")]),t._v(" "),r("el-button",{staticClass:"mr10",attrs:{size:"small",type:""},on:{click:t.resetForm}},[t._v("\n      "+t._s(t.$t("common.reset"))+"\n    ")])],1),t._v(" "),r("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"12px"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{label:t.$t("common.serialNumber"),type:"index",width:"110"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.image"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.orderId"),"min-width":"80",prop:"id"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("chainTransferRecord.nickname"),"min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.realName)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.orderNo"),"min-width":"80",prop:"orderId"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.productName"),width:"120",prop:"productName"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.payCount"),"min-width":"80",prop:"payCount"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("filterEmpty")(e.row.payCount)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.actualCommission"),width:"120",prop:"price"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.payPrice"),width:"120",prop:"totalPrice"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.commissionRate"),width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.formatRate(e.row.commissionRate)))]}}])}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.creatTime"),width:"120",prop:"createTime"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.contentId"),"min-width":"80",prop:"contentId"}}),t._v(" "),r("el-table-column",{attrs:{label:t.$t("order.search.statusLabel"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.$t("order.search."+e.row.statusCode)))]}}])})],1),t._v(" "),r("el-pagination",{staticClass:"mt20",attrs:{"current-page":t.searchFrom.page,"page-sizes":[20,40,60,100],"page-size":t.searchFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.searchFrom.total},on:{"size-change":t.sizeChange,"current-change":t.pageChange}})],1)],1)},o=[],n=r("f8b7"),i={name:"OrderSearch",data:function(){return{loading:!1,searchFrom:{orderNo:"",productTitle:"",type:"2",dateLimit:"",page:1,limit:20,total:0},tableData:[],statusList:[{value:"",label:"all"},{value:"unPaid",label:"unPaid"},{value:"notShipped",label:"notShipped"},{value:"spike",label:"spike"},{value:"bargain",label:"bargain"},{value:"complete",label:"complete"},{value:"toBeWrittenOff",label:"toBeWrittenOff"},{value:"refunding",label:"refunding"},{value:"refunded",label:"refunded"},{value:"deleted",label:"deleted"}]}},created:function(){},mounted:function(){this.getList()},methods:{getList:function(t){var e=this,r=this;this.loading=!0,this.searchFrom.page=t||this.searchFrom.page,Object(n["f"])(this.searchFrom).then((function(t){e.tableData=t.list||[],e.tableData.forEach((function(t){t.payCount=t.productList?t.productList.length:0,t.productList&&t.productList.length>0?(t.productName=t.productList[0].productName,t.actualCommission=t.productList[0].actualCommission,t.commissionRate=t.productList[0].commissionRate,t.contentId=t.productList[0].contentId,t.estimatedCommission=t.productList[0].estimatedCommission,t.price=r.formatAmount(t.productList[0].price),t.image=t.productList[0].image,t.avatar=t.productList[0].image):(t.productName="",t.actualCommission=0,t.commissionRate=0,t.contentId="",t.estimatedCommission=0,t.price=r.formatAmount(0),t.image="",t.avatar=""),t.totalPrice=r.formatAmount(t.totalPrice)})),e.searchFrom.total=t.total||0,e.loading=!1})).catch((function(){e.loading=!1,e.tableData=[],e.searchFrom.total=0}))},formatAmount:function(t){void 0==t&&(t=0);var e=(t/1e3).toFixed(3);return e},pageChange:function(t){this.searchFrom.page=t,this.getList()},sizeChange:function(t){this.searchFrom.limit=t,this.getList()},resetForm:function(){this.searchFrom={orderNo:"",productTitle:"",type:"2",dateLimit:"",page:1,limit:20,total:0},this.getList()},formatRate:function(t){return void 0==t&&(t=0),parseInt(1e4*t)/100+"%"}}},s=i,l=(r("67f7"),r("2877")),c=Object(l["a"])(s,a,o,!1,null,"15eb7fa5",null);e["default"]=c.exports},"67f7":function(t,e,r){"use strict";r("8554")},8554:function(t,e,r){},f8b7:function(t,e,r){"use strict";r.d(e,"f",(function(){return o})),r.d(e,"o",(function(){return n})),r.d(e,"g",(function(){return i})),r.d(e,"d",(function(){return s})),r.d(e,"h",(function(){return l})),r.d(e,"e",(function(){return c})),r.d(e,"i",(function(){return u})),r.d(e,"m",(function(){return d})),r.d(e,"l",(function(){return m})),r.d(e,"k",(function(){return p})),r.d(e,"v",(function(){return f})),r.d(e,"u",(function(){return h})),r.d(e,"n",(function(){return b})),r.d(e,"r",(function(){return g})),r.d(e,"s",(function(){return v})),r.d(e,"p",(function(){return _})),r.d(e,"q",(function(){return w})),r.d(e,"c",(function(){return y})),r.d(e,"a",(function(){return F})),r.d(e,"t",(function(){return $})),r.d(e,"j",(function(){return O}));var a=r("b775");function o(t){return Object(a["a"])({url:"/admin/store/order/list",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/admin/store/order/status/num",method:"get",params:t})}function i(t){return Object(a["a"])({url:"/admin/store/order/list/data",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/admin/store/order/delete",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/admin/store/order/status/list",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/store/order/info",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/admin/store/order/mark",method:"post",params:t})}function d(t){return Object(a["a"])({url:"/admin/store/order/send",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:t})}function p(t){return Object(a["a"])({url:"/admin/store/order/refund",method:"get",params:t})}function f(t){return Object(a["a"])({url:"/admin/store/order/writeUpdate/".concat(t),method:"get"})}function h(t){return Object(a["a"])({url:"/admin/store/order/writeConfirm/".concat(t),method:"get"})}function b(){return Object(a["a"])({url:"/admin/store/order/statistics",method:"get"})}function g(t){return Object(a["a"])({url:"/admin/store/order/statisticsData",method:"get",params:t})}function v(t){return Object(a["a"])({url:"admin/store/order/update/price",method:"post",data:t})}function _(t){return Object(a["a"])({url:"/admin/store/order/time",method:"get",params:t})}function w(){return Object(a["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function y(t){return Object(a["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:t})}function F(){return Object(a["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function $(t){return Object(a["a"])({url:"/admin/store/order/video/send",method:"post",data:t})}function O(t){return Object(a["a"])({url:"/admin/yly/print/".concat(t),method:"get"})}}}]);