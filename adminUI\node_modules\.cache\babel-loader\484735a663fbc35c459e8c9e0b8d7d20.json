{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\couponList\\couponFrom\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\couponList\\couponFrom\\index.js", "mtime": 1754550267775}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nrequire(\"@/styles/element-variables.scss\");\nvar _index = _interopRequireDefault(require(\"./index.vue\"));\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n_vue.default.use(_elementUi.default, {\n  size: _jsCookie.default.get('size') || 'medium' // set element-ui default size\n});\nvar couponFrom = {};\ncouponFrom.install = function (Vue, options) {\n  var ToastConstructor = Vue.extend(_index.default);\n  // 生成一个该子类的实例\n  var instance = new ToastConstructor();\n  instance.$mount(document.createElement('div'));\n  document.body.appendChild(instance.$el);\n  Vue.prototype.$modalCoupon = function (handle, keyNum) {\n    var coupons = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    var callback = arguments.length > 3 ? arguments[3] : undefined;\n    var userIds = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : '';\n    var userType = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : '';\n    instance.visible = true;\n    instance.handle = handle;\n    instance.keyNum = keyNum;\n    instance.coupons = coupons;\n    instance.userIds = userIds;\n    instance.callback = callback;\n    instance.userType = userType;\n  };\n};\nvar _default = exports.default = couponFrom;", null]}