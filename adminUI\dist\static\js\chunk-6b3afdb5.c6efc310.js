(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b3afdb5"],{"0b54":function(e,t,r){},2638:function(e,t,r){"use strict";function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,r=1;r<arguments.length;r++)for(var i in t=arguments[r],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)}var o=["attrs","props","domProps"],a=["class","style","directives"],n=["on","nativeOn"],s=function(e){return e.reduce((function(e,t){for(var r in t)if(e[r])if(-1!==o.indexOf(r))e[r]=i({},e[r],t[r]);else if(-1!==a.indexOf(r)){var s=e[r]instanceof Array?e[r]:[e[r]],d=t[r]instanceof Array?t[r]:[t[r]];e[r]=[].concat(s,d)}else if(-1!==n.indexOf(r))for(var c in t[r])if(e[r][c]){var u=e[r][c]instanceof Array?e[r][c]:[e[r][c]],m=t[r][c]instanceof Array?t[r][c]:[t[r][c]];e[r][c]=[].concat(u,m)}else e[r][c]=t[r][c];else if("hook"===r)for(var f in t[r])e[r][f]=e[r][f]?l(e[r][f],t[r][f]):t[r][f];else e[r]=t[r];else e[r]=t[r];return e}),{})},l=function(e,t){return function(){e&&e.apply(this,arguments),t&&t.apply(this,arguments)}};e.exports=s},4324:function(e,t,r){},"634a":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"divBox relative"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{size:"small","label-width":"100px"}},[e.checkPermi(["admin:order:status:num"])?r("el-form-item",{attrs:{label:"订单状态："}},[r("el-radio-group",{attrs:{type:"button"},on:{change:e.seachList},model:{value:e.tableFrom.status,callback:function(t){e.$set(e.tableFrom,"status",t)},expression:"tableFrom.status"}},[r("el-radio-button",{attrs:{label:"all"}},[e._v("全部 "+e._s(e.orderChartType.all))]),e._v(" "),r("el-radio-button",{attrs:{label:"unPaid"}},[e._v("未支付 "+e._s(e.orderChartType.unPaid))]),e._v(" "),r("el-radio-button",{attrs:{label:"notShipped"}},[e._v("未发货 "+e._s(e.orderChartType.notShipped))]),e._v(" "),r("el-radio-button",{attrs:{label:"spike"}},[e._v("待收货 "+e._s(e.orderChartType.spike))]),e._v(" "),r("el-radio-button",{attrs:{label:"bargain"}},[e._v("待评价 "+e._s(e.orderChartType.bargain))]),e._v(" "),r("el-radio-button",{attrs:{label:"complete"}},[e._v("交易完成 "+e._s(e.orderChartType.complete))]),e._v(" "),r("el-radio-button",{attrs:{label:"toBeWrittenOff"}},[e._v("待核销 "+e._s(e.orderChartType.toBeWrittenOff))]),e._v(" "),r("el-radio-button",{attrs:{label:"refunding"}},[e._v("退款中 "+e._s(e.orderChartType.refunding))]),e._v(" "),r("el-radio-button",{attrs:{label:"refunded"}},[e._v("已退款 "+e._s(e.orderChartType.refunded))]),e._v(" "),r("el-radio-button",{attrs:{label:"deleted"}},[e._v("已删除 "+e._s(e.orderChartType.deleted))])],1)],1):e._e(),e._v(" "),r("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[r("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(t){return e.selectChange(e.tableFrom.dateLimit)}},model:{value:e.tableFrom.dateLimit,callback:function(t){e.$set(e.tableFrom,"dateLimit",t)},expression:"tableFrom.dateLimit"}},e._l(e.fromList.fromTxt,(function(t,i){return r("el-radio-button",{key:i,attrs:{label:t.val}},[e._v(e._s(t.text))])})),1),e._v(" "),r("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1),e._v(" "),r("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:e.tableFrom.orderNo,callback:function(t){e.$set(e.tableFrom,"orderNo",t)},expression:"tableFrom.orderNo"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),r("el-form-item",{staticClass:"width100"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:order"],expression:"['admin:export:excel:order']"}],attrs:{size:"small"},on:{click:e.exports}},[e._v("导出")])],1)],1)],1)])]),e._v(" "),r("div",{staticClass:"mt20"}),e._v(" "),r("el-card",{staticClass:"box-card"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(e){return e.orderId}}},[r("el-table-column",{attrs:{label:"订单号","min-width":"210"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticStyle:{display:"block"},domProps:{textContent:e._s(t.row.orderId)}}),e._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:t.row.isDel,expression:"scope.row.isDel"}],staticStyle:{color:"#ED4014",display:"block"}},[e._v("用户已删除")])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"orderType",label:"订单类型","min-width":"110"}}),e._v(" "),r("el-table-column",{attrs:{prop:"realName",label:"收货人","min-width":"100"}}),e._v(" "),r("el-table-column",{attrs:{label:"商品信息","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[t.row.productList&&t.row.productList.length?r("div",{attrs:{slot:"reference"},slot:"reference"},e._l(t.row.productList,(function(t,i){return r("div",{key:i,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{staticClass:"demo-image__preview mr10"},[r("el-image",{attrs:{src:t.info.image,"preview-src-list":[t.info.image]}})],1),e._v(" "),r("div",{staticClass:"text_overflow"},[r("span",{staticClass:"tabBox_tit mr10"},[e._v(e._s(t.info.productName+" | ")+e._s(t.info.sku?t.info.sku:"-"))]),e._v(" "),r("span",{staticClass:"tabBox_pice"},[e._v(e._s(t.info.price+" x "+t.info.payNum))])])])})),0):e._e(),e._v(" "),t.row.productList&&t.row.productList.length?r("div",{staticClass:"pup_card"},e._l(t.row.productList,(function(t,i){return r("div",{key:i,staticClass:"tabBox acea-row row-middle",staticStyle:{"flex-wrap":"inherit"}},[r("div",{},[r("span",{staticClass:"tabBox_tit mr10"},[e._v(e._s(t.info.productName+" | ")+e._s(t.info.sku?t.info.sku:"-"))]),e._v(" "),r("span",{staticClass:"tabBox_pice"},[e._v(e._s(t.info.price+" x "+t.info.payNum))])])])})),0):e._e()])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"payPrice",label:"实际支付","min-width":"80"}}),e._v(" "),r("el-table-column",{attrs:{label:"支付方式","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(t.row.payTypeStr))])]}}])}),e._v(" "),r("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[1===t.row.refundStatus||2===t.row.refundStatus?r("div",{staticClass:"refunding"},[[r("el-popover",{attrs:{trigger:"hover",placement:"left","open-delay":800}},[r("b",{staticStyle:{color:"#f124c7"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.statusStr.value))]),e._v(" "),r("div",{staticClass:"pup_card flex-column"},[r("span",[e._v("退款原因："+e._s(t.row.refundReasonWap))]),e._v(" "),r("span",[e._v("备注说明："+e._s(t.row.refundReasonWapExplain))]),e._v(" "),r("span",[e._v("退款时间："+e._s(t.row.refundReasonTime))]),e._v(" "),r("span",{staticClass:"acea-row"},[e._v("\n                          退款凭证：\n                          "),t.row.refundReasonWapImg?e._l(t.row.refundReasonWapImg.split(","),(function(e,t){return r("div",{key:t,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[r("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})):r("span",{staticStyle:{display:"inline-block"}},[e._v("无")])],2)])])]],2):r("span",[e._v(e._s(t.row.statusStr.value))])])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"下单时间","min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[!1===t.row.paid?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:update:price"],expression:"['admin:order:update:price']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return e.edit(t.row)}}},[e._v("编辑")]):e._e(),e._v(" "),"notShipped"===t.row.statusStr.key&&0===t.row.refundStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:send"],expression:"['admin:order:send']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return e.sendOrder(t.row)}}},[e._v("发送货")]):e._e(),e._v(" "),"toBeWrittenOff"===t.row.statusStr.key&&1==t.row.paid&&0===t.row.refundStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:write:update"],expression:"['admin:order:write:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return e.onWriteOff(t.row)}}},[e._v("立即核销")]):e._e(),e._v(" "),r("el-dropdown",{attrs:{trigger:"click"}},[r("span",{staticClass:"el-dropdown-link"},[e._v("\n              更多"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.checkPermi(["admin:order:info"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderDetails(t.row.orderId)}}},[e._v("订单详情")]):e._e(),e._v(" "),e.checkPermi(["admin:order:status:list"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderLog(t.row.orderId)}}},[e._v("订单记录")]):e._e(),e._v(" "),e.checkPermi(["admin:order:mark"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderMark(t.row)}}},[e._v("订单备注")]):e._e(),e._v(" "),1===t.row.refundStatus&&e.checkPermi(["admin:order:refund:refuse"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderRefuse(t.row)}}},[e._v("拒绝退款")]):e._e(),e._v(" "),1===t.row.refundStatus&&e.checkPermi(["admin:order:refund"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderRefund(t.row)}}},[e._v("立即退款")]):e._e(),e._v(" "),"deleted"===t.row.statusStr.key&&e.checkPermi(["admin:order:delete"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.handleDelete(t.row,t.$index)}}},[e._v("删除订单")]):e._e(),e._v(" "),"unPaid"!==t.row.statusStr.key?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderPrint(t.row)}}},[e._v("打印小票")]):e._e()],1)],1)]}}])})],1),e._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"编辑订单",visible:e.dialogVisible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogVisible?r("zb-parser",{attrs:{"form-id":104,"is-create":e.isCreate,"edit-data":e.editData},on:{submit:e.handlerSubmit,resetForm:e.resetForm}}):e._e()],1),e._v(" "),r("el-dialog",{attrs:{title:"操作记录",visible:e.dialogVisibleJI,width:"700px"},on:{"update:visible":function(t){e.dialogVisibleJI=t}}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.LogLoading,expression:"LogLoading"}],staticStyle:{width:"100%"},attrs:{border:"",data:e.tableDataLog.data}},[r("el-table-column",{attrs:{prop:"oid",align:"center",label:"ID","min-width":"80"}}),e._v(" "),r("el-table-column",{attrs:{prop:"changeMessage",label:"操作记录",align:"center","min-width":"280"}}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"操作时间",align:"center","min-width":"280"}})],1),e._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFromLog.limit,"current-page":e.tableFromLog.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableDataLog.total},on:{"size-change":e.handleSizeChangeLog,"current-change":e.pageChangeLog}})],1)],1),e._v(" "),r("details-from",{ref:"orderDetail",attrs:{orderId:e.orderId}}),e._v(" "),r("order-send",{ref:"send",attrs:{orderId:e.orderId},on:{submitFail:e.getList}}),e._v(" "),r("order-video-send",{ref:"videoSend",attrs:{orderId:e.orderId},on:{submitFail:e.getList}}),e._v(" "),e.RefuseVisible?r("el-dialog",{attrs:{title:"拒绝退款原因",visible:e.RefuseVisible,width:"500px","before-close":e.RefusehandleClose},on:{"update:visible":function(t){e.RefuseVisible=t}}},[r("zb-parser",{attrs:{"form-id":106,"is-create":1,"edit-data":e.RefuseData},on:{submit:e.RefusehandlerSubmit,resetForm:e.resetFormRefusehand}})],1):e._e(),e._v(" "),r("el-dialog",{attrs:{title:"退款处理",visible:e.refundVisible,width:"500px","before-close":e.refundhandleClose},on:{"update:visible":function(t){e.refundVisible=t}}},[e.refundVisible?r("zb-parser",{attrs:{"form-id":107,"is-create":1,"edit-data":e.refundData},on:{submit:e.refundhandlerSubmit,resetForm:e.resetFormRefundhandler}}):e._e()],1)],1)},o=[],a=r("f8b7"),n=r("0f56"),s=r("a356"),l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.orderDatalist?i("el-dialog",{attrs:{title:"订单信息",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"description"},[i("div",{staticClass:"title"},[e._v("用户信息")]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v("用户昵称："+e._s(e.orderDatalist.nikeName))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("绑定电话："+e._s(e.orderDatalist.phone?e.orderDatalist.phone:"无"))])]),e._v(" "),i("el-divider"),e._v(" "),i("div",{staticClass:"title"},[e._v(e._s("toBeWrittenOff"===e.orderDatalist.statusStr.key?"提货信息":"收货信息"))]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v(e._s("toBeWrittenOff"===e.orderDatalist.statusStr.key?"提货人":"收货人")+"："+e._s(e.orderDatalist.realName))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v(e._s("toBeWrittenOff"===e.orderDatalist.statusStr.key?"提货电话":"收货电话")+"："+e._s(e.orderDatalist.userPhone))]),e._v(" "),"toBeWrittenOff"!==e.orderDatalist.statusStr.key?i("div",{staticClass:"description-term"},[e._v(e._s("toBeWrittenOff"===e.orderDatalist.statusStr.key?"提货地址":"收货地址")+"："+e._s(e.orderDatalist.userAddress))]):e._e()]),e._v(" "),i("el-divider"),e._v(" "),i("div",{staticClass:"title"},[e._v("订单信息")]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v("订单编号："+e._s(e.orderDatalist.orderId))]),e._v(" "),i("div",{staticClass:"description-term",staticStyle:{color:"red"}},[e._v("订单状态："+e._s(e.orderDatalist.statusStr.value))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("商品总数："+e._s(e.orderDatalist.totalNum))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("商品总价："+e._s(e.orderDatalist.proTotalPrice))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("支付邮费："+e._s(e.orderDatalist.payPostage))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("优惠券金额："+e._s(e.orderDatalist.couponPrice))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("实际支付："+e._s(e.orderDatalist.payPrice))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("抵扣金额："+e._s(e.orderDatalist.deductionPrice))]),e._v(" "),e.orderDatalist.refundPrice?i("div",{staticClass:"description-term fontColor3"},[e._v("退款金额："+e._s(e.orderDatalist.refundPrice))]):e._e(),e._v(" "),e.orderDatalist.useIntegral?i("div",{staticClass:"description-term"},[e._v("使用积分："+e._s(e.orderDatalist.useIntegral))]):e._e(),e._v(" "),e.orderDatalist.backIntegral?i("div",{staticClass:"description-term"},[e._v("退回积分："+e._s(e.orderDatalist.backIntegral))]):e._e(),e._v(" "),i("div",{staticClass:"description-term"},[e._v("创建时间："+e._s(e.orderDatalist.createTime))]),e._v(" "),e.orderDatalist.refundReasonTime?i("div",{staticClass:"description-term"},[e._v("退款时间："+e._s(e.orderDatalist.refundReasonTime))]):e._e(),e._v(" "),i("div",{staticClass:"description-term"},[e._v("支付方式："+e._s(e.orderDatalist.payTypeStr))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("推广人："+e._s(e._f("filterEmpty")(e.orderDatalist.spreadName)))]),e._v(" "),2===e.orderDatalist.shippingType&&"notShipped"===e.orderDatalist.statusStr.key?i("div",{staticClass:"description-term"},[e._v("门店名称："+e._s(e.orderDatalist.storeName))]):e._e(),e._v(" "),2===e.orderDatalist.shippingType&&"notShipped"===e.orderDatalist.statusStr.key?i("div",{staticClass:"description-term"},[e._v("核销码："+e._s(e.orderDatalist.user_phone))]):e._e(),e._v(" "),i("div",{staticClass:"description-term"},[e._v("商家备注："+e._s(e.orderDatalist.remark))]),e._v(" "),"toBeWrittenOff"===e.orderDatalist.statusStr.key&&e.orderDatalist.systemStore?[i("div",{staticClass:"description-term"},[e._v("提货码："+e._s(e.orderDatalist.verifyCode))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("门店名称："+e._s(e.orderDatalist.systemStore.name))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("门店电话："+e._s(e.orderDatalist.systemStore.phone))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("门店地址："+e._s(e.orderDatalist.systemStore.address+e.orderDatalist.systemStore.detailedAddress))])]:e._e()],2),e._v(" "),"express"===e.orderDatalist.deliveryType?[i("el-divider"),e._v(" "),i("div",{staticClass:"title"},[e._v("物流信息")]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v("快递公司："+e._s(e.orderDatalist.deliveryName))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("快递单号："+e._s(e.orderDatalist.deliveryId)+"\n            "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:logistics:info"],expression:"['admin:order:logistics:info']"}],staticStyle:{"margin-left":"5px"},attrs:{type:"primary",size:"mini"},on:{click:e.openLogistics}},[e._v("物流查询")])],1)])]:e._e(),e._v(" "),"send"===e.orderDatalist.deliveryType?[i("el-divider"),e._v(" "),i("div",{staticClass:"title"},[e._v("配送信息")]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v("送货人姓名："+e._s(e.orderDatalist.deliveryName))]),e._v(" "),i("div",{staticClass:"description-term"},[e._v("送货人电话："+e._s(e.orderDatalist.deliveryId))])])]:e._e(),e._v(" "),e.orderDatalist.mark?[i("el-divider"),e._v(" "),i("div",{staticClass:"title"},[e._v("用户备注")]),e._v(" "),i("div",{staticClass:"acea-row"},[i("div",{staticClass:"description-term"},[e._v(e._s(e.orderDatalist.mark))])])]:e._e()],2)]):e._e(),e._v(" "),e.orderDatalist?i("el-dialog",{attrs:{title:"提示",visible:e.modal2,width:"30%"},on:{"update:visible":function(t){e.modal2=t}}},[i("div",{staticClass:"logistics acea-row row-top"},[i("div",{staticClass:"logistics_img"},[i("img",{attrs:{src:r("df87")}})]),e._v(" "),i("div",{staticClass:"logistics_cent"},[i("span",{staticClass:"mb10"},[e._v("物流公司："+e._s(e.orderDatalist.deliveryName))]),e._v(" "),i("span",[e._v("物流单号："+e._s(e.orderDatalist.deliveryId))])])]),e._v(" "),i("div",{staticClass:"acea-row row-column-around trees-coadd"},[i("div",{staticClass:"scollhide"},[i("el-timeline",{attrs:{reverse:e.reverse}},e._l(e.result,(function(t,r){return i("el-timeline-item",{key:r},[i("p",{staticClass:"time",domProps:{textContent:e._s(t.time)}}),e._v(" "),i("p",{staticClass:"content",domProps:{textContent:e._s(t.status)}})])})),1)],1)]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.modal2=!1}}},[e._v("关闭")])],1)]):e._e()],1)},d=[];function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function a(r,i,o,a){var l=i&&i.prototype instanceof s?i:s,d=Object.create(l.prototype);return u(d,"_invoke",function(r,i,o){var a,s,l,d=0,c=o||[],u=!1,m={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,r){return a=t,s=0,l=e,m.n=r,n}};function f(r,i){for(s=r,l=i,t=0;!u&&d&&!o&&t<c.length;t++){var o,a=c[t],f=m.p,p=a[2];r>3?(o=p===i)&&(l=a[(s=a[4])?5:(s=3,3)],a[4]=a[5]=e):a[0]<=f&&((o=r<2&&f<a[1])?(s=0,m.v=i,m.n=a[1]):f<p&&(o=r<3||a[0]>i||i>p)&&(a[4]=r,a[5]=i,m.n=p,s=0))}if(o||r>1)return n;throw u=!0,i}return function(o,c,p){if(d>1)throw TypeError("Generator is already running");for(u&&1===c&&f(c,p),s=c,l=p;(t=s<2?e:l)||!u;){a||(s?s<3?(s>1&&(m.n=-1),f(s,l)):m.n=l:m.v=l);try{if(d=2,a){if(s||(o="next"),t=a[o]){if(!(t=t.call(a,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=a.return)&&t.call(a),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);a=e}else if((t=(u=m.n<0)?l:r.call(i,m))!==n)break}catch(t){a=e,s=1,l=t}finally{d=1}}return{value:t,done:u}}}(r,o,a),!0),d}var n={};function s(){}function l(){}function d(){}t=Object.getPrototypeOf;var m=[][i]?t(t([][i]())):(u(t={},i,(function(){return this})),t),f=d.prototype=s.prototype=Object.create(m);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,u(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return l.prototype=d,u(f,"constructor",d),u(d,"constructor",l),l.displayName="GeneratorFunction",u(d,o,"GeneratorFunction"),u(f),u(f,o,"Generator"),u(f,i,(function(){return this})),u(f,"toString",(function(){return"[object Generator]"})),(c=function(){return{w:a,m:p}})()}function u(e,t,r,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}u=function(e,t,r,i){function a(t,r){u(e,t,(function(e){return this._invoke(t,r,e)}))}t?o?o(e,t,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[t]=r:(a("next",0),a("throw",1),a("return",2))},u(e,t,r,i)}function m(e,t,r,i,o,a,n){try{var s=e[a](n),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function f(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function n(e){m(a,i,o,n,s,"next",e)}function s(e){m(a,i,o,n,s,"throw",e)}n(void 0)}))}}var p={name:"OrderDetail",props:{orderId:{type:String,default:0}},data:function(){return{reverse:!0,dialogVisible:!1,orderDatalist:null,loading:!1,modal2:!1,result:[]}},mounted:function(){},methods:{openLogistics:function(){this.getOrderData(),this.modal2=!0},getOrderData:function(){var e=this;Object(a["c"])({orderNo:this.orderId}).then(function(){var t=f(c().m((function t(r){return c().w((function(t){while(1)switch(t.n){case 0:e.result=r.list;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},getDetail:function(e){var t=this;this.loading=!0,Object(a["e"])({orderNo:e}).then((function(e){t.orderDatalist=e,t.loading=!1})).catch((function(){t.orderDatalist=null,t.loading=!1}))}}},v=p,h=(r("c2c0"),r("2877")),b=Object(h["a"])(v,l,d,!1,null,"312b6dab",null),g=b.exports,_=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{staticClass:"order_box",attrs:{visible:e.modals,title:"发送货","before-close":e.handleClose,width:"600px"},on:{"update:visible":function(t){e.modals=t}}},[r("el-form",{ref:"formItem",attrs:{model:e.formItem,"label-width":"110px",rules:e.rules},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{label:"选择类型："}},[r("el-radio-group",{on:{change:function(t){return e.changeRadioType(e.formItem.type)}},model:{value:e.formItem.type,callback:function(t){e.$set(e.formItem,"type",t)},expression:"formItem.type"}},[r("el-radio",{attrs:{label:"1"}},[e._v("发货")]),e._v(" "),r("el-radio",{attrs:{label:"2"}},[e._v("送货")]),e._v(" "),r("el-radio",{attrs:{label:"3"}},[e._v("虚拟")])],1)],1),e._v(" "),"1"===e.formItem.type?r("div",[r("el-form-item",{attrs:{label:"发货类型：",prop:"expressId"}},[r("el-radio-group",{on:{change:function(t){return e.changeRadio(e.formItem.expressRecordType)}},model:{value:e.formItem.expressRecordType,callback:function(t){e.$set(e.formItem,"expressRecordType",t)},expression:"formItem.expressRecordType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("手动填写")]),e._v(" "),e.checkPermi(["admin:order:sheet:info"])?r("el-radio",{attrs:{label:"2"}},[e._v("电子面单打印")]):e._e()],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"快递公司：",prop:"expressCode"}},[r("el-select",{staticStyle:{width:"80%"},attrs:{filterable:""},on:{change:function(t){return e.onChangeExport(e.formItem.expressCode)}},model:{value:e.formItem.expressCode,callback:function(t){e.$set(e.formItem,"expressCode",t)},expression:"formItem.expressCode"}},e._l(e.express,(function(e,t){return r("el-option",{key:t,attrs:{value:e.code,label:e.name}})})),1)],1),e._v(" "),"1"===e.formItem.expressRecordType?r("el-form-item",{attrs:{label:"快递单号：",prop:"expressNumber"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:e.formItem.expressNumber,callback:function(t){e.$set(e.formItem,"expressNumber",t)},expression:"formItem.expressNumber"}})],1):e._e(),e._v(" "),"2"===e.formItem.expressRecordType?[r("el-form-item",{staticClass:"express_temp_id",attrs:{label:"电子面单：",prop:"expressTempId"}},[r("div",{staticClass:"acea-row"},[r("el-select",{class:[e.formItem.expressTempId?"width9":"width8"],attrs:{placeholder:"请选择电子面单"},on:{change:e.onChangeImg},model:{value:e.formItem.expressTempId,callback:function(t){e.$set(e.formItem,"expressTempId",t)},expression:"formItem.expressTempId"}},e._l(e.exportTempList,(function(e,t){return r("el-option",{key:t,attrs:{value:e.temp_id,label:e.title}})})),1),e._v(" "),e.formItem.expressTempId?r("div",{staticStyle:{position:"relative"}},[r("div",{staticClass:"tempImgList ml10"},[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.tempImg,"preview-src-list":[e.tempImg]}})],1)])]):e._e()],1)]),e._v(" "),r("el-form-item",{attrs:{label:"寄件人姓名：",prop:"toName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:e.formItem.toName,callback:function(t){e.$set(e.formItem,"toName",t)},expression:"formItem.toName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"寄件人电话：",prop:"toTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:e.formItem.toTel,callback:function(t){e.$set(e.formItem,"toTel",t)},expression:"formItem.toTel"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"寄件人地址：",prop:"toAddr"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:e.formItem.toAddr,callback:function(t){e.$set(e.formItem,"toAddr",t)},expression:"formItem.toAddr"}})],1)]:e._e()],2):e._e(),e._v(" "),"2"===e.formItem.type?r("div",[r("el-form-item",{attrs:{label:"送货人姓名：",prop:"deliveryName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人姓名"},model:{value:e.formItem.deliveryName,callback:function(t){e.$set(e.formItem,"deliveryName",t)},expression:"formItem.deliveryName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"送货人电话：",prop:"deliveryTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人电话"},model:{value:e.formItem.deliveryTel,callback:function(t){e.$set(e.formItem,"deliveryTel",t)},expression:"formItem.deliveryTel"}})],1)],1):e._e(),e._v(" "),r("div",[r("el-form-item",{attrs:{label:""}},[r("div",{staticStyle:{color:"#CECECE"}},[e._v("顺丰请输入单号：收件人或寄件人手机号后四位")]),e._v(" "),r("div",{staticStyle:{color:"#CECECE"}},[e._v("例如：SF000000000000:3941")])])],1)],1),e._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.putSend("formItem")}}},[e._v("提交")]),e._v(" "),r("el-button",{on:{click:function(t){return e.cancel("formItem")}}},[e._v("取消")])],1)],1)},y=[],w=r("b61d"),x=r("e350"),C=r("61f7");function I(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function a(r,i,o,a){var l=i&&i.prototype instanceof s?i:s,d=Object.create(l.prototype);return O(d,"_invoke",function(r,i,o){var a,s,l,d=0,c=o||[],u=!1,m={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,r){return a=t,s=0,l=e,m.n=r,n}};function f(r,i){for(s=r,l=i,t=0;!u&&d&&!o&&t<c.length;t++){var o,a=c[t],f=m.p,p=a[2];r>3?(o=p===i)&&(l=a[(s=a[4])?5:(s=3,3)],a[4]=a[5]=e):a[0]<=f&&((o=r<2&&f<a[1])?(s=0,m.v=i,m.n=a[1]):f<p&&(o=r<3||a[0]>i||i>p)&&(a[4]=r,a[5]=i,m.n=p,s=0))}if(o||r>1)return n;throw u=!0,i}return function(o,c,p){if(d>1)throw TypeError("Generator is already running");for(u&&1===c&&f(c,p),s=c,l=p;(t=s<2?e:l)||!u;){a||(s?s<3?(s>1&&(m.n=-1),f(s,l)):m.n=l:m.v=l);try{if(d=2,a){if(s||(o="next"),t=a[o]){if(!(t=t.call(a,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=a.return)&&t.call(a),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);a=e}else if((t=(u=m.n<0)?l:r.call(i,m))!==n)break}catch(t){a=e,s=1,l=t}finally{d=1}}return{value:t,done:u}}}(r,o,a),!0),d}var n={};function s(){}function l(){}function d(){}t=Object.getPrototypeOf;var c=[][i]?t(t([][i]())):(O(t={},i,(function(){return this})),t),u=d.prototype=s.prototype=Object.create(c);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,O(e,o,"GeneratorFunction")),e.prototype=Object.create(u),e}return l.prototype=d,O(u,"constructor",d),O(d,"constructor",l),l.displayName="GeneratorFunction",O(d,o,"GeneratorFunction"),O(u),O(u,o,"Generator"),O(u,i,(function(){return this})),O(u,"toString",(function(){return"[object Generator]"})),(I=function(){return{w:a,m:m}})()}function O(e,t,r,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}O=function(e,t,r,i){function a(t,r){O(e,t,(function(e){return this._invoke(t,r,e)}))}t?o?o(e,t,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[t]=r:(a("next",0),a("throw",1),a("return",2))},O(e,t,r,i)}function D(e,t,r,i,o,a,n){try{var s=e[a](n),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function S(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function n(e){D(a,i,o,n,s,"next",e)}function s(e){D(a,i,o,n,s,"throw",e)}n(void 0)}))}}var T=function(e,t,r){if(!t)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(t)?r():r(new Error("手机号格式不正确!"))},k={name:"orderSend",props:{orderId:String},data:function(){return{formItem:{type:"1",expressRecordType:"1",expressId:"",expressCode:"",deliveryName:"",deliveryTel:"",expressNumber:"",expressTempId:"",toAddr:"",toName:"",toTel:"",orderNo:""},modals:!1,express:[],exportTempList:[],tempImg:"",rules:{toName:[{required:!0,message:"请输寄件人姓名",trigger:"blur"}],toTel:[{required:!0,validator:T,trigger:"blur"}],toAddr:[{required:!0,message:"请输入寄件人地址",trigger:"blur"}],expressCode:[{required:!0,message:"请选择快递公司",trigger:"change"}],expressNumber:[{required:!0,message:"请输入快递单号",trigger:"blur"}],expressTempId:[{required:!0,message:"请选择电子面单",trigger:"change"}],deliveryName:[{required:!0,message:"请输入送货人姓名",trigger:"blur"}],deliveryTel:[{required:!0,validator:T,trigger:"blur"}]},expressType:"normal"}},mounted:function(){},methods:{checkPermi:x["a"],sheetInfo:function(){var e=this;Object(a["q"])().then(function(){var t=S(I().m((function t(r){return I().w((function(t){while(1)switch(t.n){case 0:e.formItem.toAddr=r.exportToAddress||"",e.formItem.toName=r.exportToName||"",e.formItem.toTel=r.exportToTel||"";case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},onChangeExport:function(e){this.formItem.expressTempId="","2"===this.formItem.expressRecordType&&this.exportTemp(e)},exportTemp:function(e){var t=this;Object(w["c"])({com:e}).then(function(){var e=S(I().m((function e(r){return I().w((function(e){while(1)switch(e.n){case 0:t.exportTempList=r.data.data||[];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},onChangeImg:function(e){var t=this;this.exportTempList.map((function(r){r.temp_id===e&&(t.tempImg=r.pic)}))},changeRadioType:function(){this.formItem.expressId="",this.formItem.expressCode=""},changeRadio:function(e){this.expressType=2==e?"elec":"normal",this.formItem.expressId="",this.formItem.expressCode="",this.getList()},getList:function(){var e=this;Object(w["d"])({type:this.expressType}).then(function(){var t=S(I().m((function t(r){return I().w((function(t){while(1)switch(t.n){case 0:e.express=r;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},putSend:Object(C["a"])((function(e){var t=this;this.formItem.orderNo=this.orderId,this.$refs[e].validate((function(r){r?Object(a["m"])(t.formItem).then((function(r){t.$message.success("发送货成功"),t.modals=!1,t.$refs[e].resetFields(),t.$emit("submitFail")})):t.$message.error("请填写信息")}))})),handleClose:function(){this.cancel("formItem")},cancel:function(e){this.modals=!1,this.$refs[e].resetFields(),this.formItem.type="1",this.formItem.expressRecordType="1"}}},L=k,j=(r("fa4e"),Object(h["a"])(L,_,y,!1,null,"b1502590",null)),N=j.exports,F=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{staticClass:"order_box",attrs:{visible:e.modals,title:"发送货","before-close":e.handleClose,width:"600px"},on:{"update:visible":function(t){e.modals=t}}},[r("el-form",{ref:"formItem",attrs:{model:e.formItem,"label-width":"110px",rules:e.rules},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{label:"快递公司：",prop:"expressCode"}},[r("el-select",{staticStyle:{width:"80%"},attrs:{filterable:""},model:{value:e.formItem.deliveryId,callback:function(t){e.$set(e.formItem,"deliveryId",t)},expression:"formItem.deliveryId"}},e._l(e.express,(function(e,t){return r("el-option",{key:t,attrs:{value:e.deliveryId,label:e.deliveryName}})})),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"快递单号：",prop:"waybillId"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:e.formItem.waybillId,callback:function(t){e.$set(e.formItem,"waybillId",t)},expression:"formItem.waybillId"}})],1)],1),e._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:order:video:send"],expression:"['admin:order:video:send']"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.putSend("formItem")}}},[e._v("提交")]),e._v(" "),r("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.cancel("formItem")}}},[e._v("取消")])],1)],1)},A=[];function P(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function a(r,i,o,a){var l=i&&i.prototype instanceof s?i:s,d=Object.create(l.prototype);return V(d,"_invoke",function(r,i,o){var a,s,l,d=0,c=o||[],u=!1,m={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,r){return a=t,s=0,l=e,m.n=r,n}};function f(r,i){for(s=r,l=i,t=0;!u&&d&&!o&&t<c.length;t++){var o,a=c[t],f=m.p,p=a[2];r>3?(o=p===i)&&(l=a[(s=a[4])?5:(s=3,3)],a[4]=a[5]=e):a[0]<=f&&((o=r<2&&f<a[1])?(s=0,m.v=i,m.n=a[1]):f<p&&(o=r<3||a[0]>i||i>p)&&(a[4]=r,a[5]=i,m.n=p,s=0))}if(o||r>1)return n;throw u=!0,i}return function(o,c,p){if(d>1)throw TypeError("Generator is already running");for(u&&1===c&&f(c,p),s=c,l=p;(t=s<2?e:l)||!u;){a||(s?s<3?(s>1&&(m.n=-1),f(s,l)):m.n=l:m.v=l);try{if(d=2,a){if(s||(o="next"),t=a[o]){if(!(t=t.call(a,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=a.return)&&t.call(a),s<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),s=1);a=e}else if((t=(u=m.n<0)?l:r.call(i,m))!==n)break}catch(t){a=e,s=1,l=t}finally{d=1}}return{value:t,done:u}}}(r,o,a),!0),d}var n={};function s(){}function l(){}function d(){}t=Object.getPrototypeOf;var c=[][i]?t(t([][i]())):(V(t={},i,(function(){return this})),t),u=d.prototype=s.prototype=Object.create(c);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,V(e,o,"GeneratorFunction")),e.prototype=Object.create(u),e}return l.prototype=d,V(u,"constructor",d),V(d,"constructor",l),l.displayName="GeneratorFunction",V(d,o,"GeneratorFunction"),V(u),V(u,o,"Generator"),V(u,i,(function(){return this})),V(u,"toString",(function(){return"[object Generator]"})),(P=function(){return{w:a,m:m}})()}function V(e,t,r,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}V=function(e,t,r,i){function a(t,r){V(e,t,(function(e){return this._invoke(t,r,e)}))}t?o?o(e,t,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[t]=r:(a("next",0),a("throw",1),a("return",2))},V(e,t,r,i)}function R(e,t,r,i,o,a,n){try{var s=e[a](n),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function E(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function n(e){R(a,i,o,n,s,"next",e)}function s(e){R(a,i,o,n,s,"throw",e)}n(void 0)}))}}var G={name:"orderSend",props:{orderId:String},data:function(){return{formItem:{deliveryId:"",orderNo:"",waybillId:""},modals:!1,express:[],exportTempList:[],tempImg:"",rules:{deliveryId:[{required:!0,message:"请选择快递公司",trigger:"change"}],waybillId:[{required:!0,message:"请输入快递单号",trigger:"blur"}]},expressType:"normal"}},mounted:function(){this.express=JSON.parse(sessionStorage.getItem("videoExpress"))},methods:{companyGetList:function(){var e=this;Object(a["a"])().then(function(){var t=E(P().m((function t(r){return P().w((function(t){while(1)switch(t.n){case 0:e.express=r,sessionStorage.setItem("videoExpress",JSON.stringify(r));case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},putSend:Object(C["a"])((function(e){var t=this;this.formItem.orderNo=this.orderId,this.$refs[e].validate((function(r){r?Object(a["t"])(t.formItem).then((function(r){t.$message.success("发送货成功"),t.modals=!1,t.$refs[e].resetFields(),t.$emit("submitFail")})):t.$message.error("请填写信息")}))})),handleClose:function(){this.cancel("formItem")},cancel:function(e){this.modals=!1,this.$refs[e].resetFields(),this.formItem.type="1",this.formItem.expressRecordType="1"}}},B=G,z=(r("88ce"),Object(h["a"])(B,F,A,!1,null,"d2116198",null)),W=z.exports,M=(r("6537"),r("a78e"),r("ed08")),q=r("73f5"),J={name:"orderlistDetails",components:{cardsData:n["a"],zbParser:s["a"],detailsFrom:g,orderSend:N,orderVideoSend:W},data:function(){return{RefuseVisible:!1,RefuseData:{},orderId:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{status:"all",dateLimit:"",orderNo:"",page:1,limit:10,type:0},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,fromType:[{value:"all",text:"全部"},{value:"info",text:"普通"},{value:"pintuan",text:"拼团"},{value:"bragin",text:"砍价"},{value:"miaosha",text:"秒杀"}],selectionList:[],ids:"",orderids:"",cardLists:[],isWriteOff:Object(M["e"])(),proType:0,active:!1}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:x["a"],resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(e){this.dialogVisible=!1},onWriteOff:function(e){var t=this;this.$modalSure("核销订单吗").then((function(){Object(a["v"])(e.verifyCode).then((function(){t.$message.success("核销成功"),t.tableFrom.page=1,t.getList()}))}))},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},RefusehandleClose:function(){this.RefuseVisible=!1},onOrderRefuse:function(e){this.orderids=e.orderId,this.RefuseData={orderId:e.orderId,reason:""},this.RefuseVisible=!0},RefusehandlerSubmit:function(e){var t=this;Object(a["l"])({orderNo:this.orderids,reason:e.reason}).then((function(e){t.$message.success("操作成功"),t.RefuseVisible=!1,t.getList()}))},refundhandleClose:function(){this.refundVisible=!1},onOrderRefund:function(e){this.refundData={orderId:e.orderId,amount:e.payPrice,type:""},this.orderids=e.orderId,this.refundVisible=!0},refundhandlerSubmit:function(e){var t=this;Object(a["k"])({amount:e.amount,orderNo:this.orderids}).then((function(e){t.$message.success("操作成功"),t.refundVisible=!1,t.getList()}))},sendOrder:function(e){0===e.type?(this.$refs.send.modals=!0,this.$refs.send.getList(),this.$refs.send.sheetInfo()):(this.$refs.videoSend.modals=!0,JSON.parse(sessionStorage.getItem("videoExpress"))||this.$refs.videoSend.companyGetList()),this.orderId=e.orderId},handleDelete:function(e,t){var r=this;e.isDel?this.$modalSure().then((function(){Object(a["d"])({orderNo:e.orderId}).then((function(){r.$message.success("删除成功"),r.tableData.data.splice(t,1)}))})):this.$confirm("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！","提示",{confirmButtonText:"确定",type:"error"})},onOrderDetails:function(e){this.orderId=e,this.$refs.orderDetail.getDetail(e),this.$refs.orderDetail.dialogVisible=!0},onOrderLog:function(e){var t=this;this.dialogVisibleJI=!0,this.LogLoading=!0,this.tableFromLog.orderNo=e,Object(a["h"])(this.tableFromLog).then((function(e){t.tableDataLog.data=e.list,t.tableDataLog.total=e.total,t.LogLoading=!1})).catch((function(){t.LogLoading=!1}))},pageChangeLog:function(e){this.tableFromLog.page=e,this.onOrderLog()},handleSizeChangeLog:function(e){this.tableFromLog.limit=e,this.onOrderLog()},handleClose:function(){this.dialogVisible=!1},onOrderMark:function(e){var t=this;this.$prompt("订单备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入订单备注",inputType:"textarea",inputValue:e.remark,inputPlaceholder:"请输入订单备注",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(r){var i=r.value;Object(a["i"])({mark:i,orderNo:e.orderId}).then((function(){t.$message.success("操作成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},handleSelectionChange:function(e){this.selectionList=e;var t=[];this.selectionList.map((function(e){t.push(e.orderId)})),this.ids=t.join(",")},selectChange:function(e){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(e){this.timeVal=e,this.tableFrom.dateLimit=e?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},edit:function(e){this.orderId=e.orderId,this.editData={orderId:e.orderId,totalPrice:e.totalPrice,totalPostage:e.totalPostage,payPrice:e.payPrice,payPostage:e.payPostage,gainIntegral:e.gainIntegral},this.dialogVisible=!0},handlerSubmit:function(e){var t=this,r={orderNo:e.orderId,payPrice:e.payPrice};Object(a["s"])(r).then((function(e){t.$message.success("编辑数据成功"),t.dialogVisible=!1,t.getList()}))},getList:function(){var e=this;this.listLoading=!0,Object(a["f"])(this.tableFrom).then((function(t){e.tableData.data=t.list||[],e.tableData.total=t.total,e.listLoading=!1,e.checkedCities=e.$cache.local.has("order_stroge")?e.$cache.local.getJSON("order_stroge"):e.checkedCities})).catch((function(){e.listLoading=!1}))},getOrderListData:function(){var e=this;Object(a["g"])({dateLimit:this.tableFrom.dateLimit}).then((function(t){e.cardLists=[{name:"订单数量",count:t.count,color:"#1890FF",class:"one",icon:"icondingdan"},{name:"订单金额",count:t.amount,color:"#A277FF",class:"two",icon:"icondingdanjine"},{name:"微信支付金额",count:t.weChatAmount,color:"#EF9C20",class:"three",icon:"iconweixinzhifujine"},{name:"余额支付金额",count:t.yueAmount,color:"#1BBE6B",class:"four",icon:"iconyuezhifujine2"}]}))},getOrderStatusNum:function(){var e=this;Object(a["o"])({dateLimit:this.tableFrom.dateLimit,type:this.tableFrom.type}).then((function(t){e.orderChartType=t}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},exports:function(){var e={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,status:this.tableFrom.status,type:this.tableFrom.type};Object(q["i"])(e).then((function(e){window.open(e.fileName)}))},onOrderPrint:function(e){var t=this;Object(a["j"])(e.orderId).then((function(e){t.$modal.msgSuccess("打印成功")})).catch((function(e){t.$modal.msgError(e.message)}))}}},Q=J,$=(r("bde5"),Object(h["a"])(Q,i,o,!1,null,"6de28303",null));t["default"]=$.exports},"88ce":function(e,t,r){"use strict";r("0b54")},"92c6":function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return a})),r.d(t,"d",(function(){return n})),r.d(t,"a",(function(){return s})),r.d(t,"f",(function(){return l})),r.d(t,"g",(function(){return d})),r.d(t,"j",(function(){return c})),r.d(t,"h",(function(){return u})),r.d(t,"e",(function(){return m})),r.d(t,"i",(function(){return f}));var i=r("b775");function o(e){var t={id:e.id};return Object(i["a"])({url:"/admin/system/form/temp/info",method:"GET",params:t})}function a(e){var t={keywords:e.keywords,page:e.page,limit:e.limit};return Object(i["a"])({url:"/admin/system/form/temp/list",method:"GET",params:t})}function n(e){var t={content:e.content,info:e.info,name:e.name};return Object(i["a"])({url:"/admin/system/form/temp/save",method:"POST",data:t})}function s(e){var t={id:e.id},r={content:e.content,info:e.info,name:e.name};return Object(i["a"])({url:"/admin/system/form/temp/update",method:"POST",params:t,data:r})}function l(e){var t={sendType:e.sendType};return Object(i["a"])({url:"/admin/system/notification/list",method:"GET",params:t})}function d(e){return Object(i["a"])({url:"/admin/system/notification/routine/switch/".concat(e),method:"post"})}function c(e){return Object(i["a"])({url:"/admin/system/notification/wechat/switch/".concat(e),method:"post"})}function u(e){return Object(i["a"])({url:"/admin/system/notification/sms/switch/".concat(e),method:"post"})}function m(e){var t={detailType:e.type,id:e.id};return Object(i["a"])({url:"/admin/system/notification/detail",method:"get",params:t})}function f(e){var t={detailType:e.type,id:e.id,status:e.status,tempId:e.tempId};return Object(i["a"])({url:"/admin/system/notification/update",method:"post",data:t})}},bde5:function(e,t,r){"use strict";r("bf63")},bf63:function(e,t,r){},c2c0:function(e,t,r){"use strict";r("d760")},d760:function(e,t,r){},df87:function(e,t){e.exports="data:image/jpeg;base64,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"},f8b7:function(e,t,r){"use strict";r.d(t,"f",(function(){return o})),r.d(t,"o",(function(){return a})),r.d(t,"g",(function(){return n})),r.d(t,"d",(function(){return s})),r.d(t,"h",(function(){return l})),r.d(t,"e",(function(){return d})),r.d(t,"i",(function(){return c})),r.d(t,"m",(function(){return u})),r.d(t,"l",(function(){return m})),r.d(t,"k",(function(){return f})),r.d(t,"v",(function(){return p})),r.d(t,"u",(function(){return v})),r.d(t,"n",(function(){return h})),r.d(t,"r",(function(){return b})),r.d(t,"s",(function(){return g})),r.d(t,"p",(function(){return _})),r.d(t,"q",(function(){return y})),r.d(t,"c",(function(){return w})),r.d(t,"a",(function(){return x})),r.d(t,"t",(function(){return C})),r.d(t,"j",(function(){return I}));var i=r("b775");function o(e){return Object(i["a"])({url:"/admin/store/order/list",method:"get",params:e})}function a(e){return Object(i["a"])({url:"/admin/store/order/status/num",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/admin/store/order/list/data",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/admin/store/order/delete",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/admin/store/order/status/list",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/admin/store/order/info",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/admin/store/order/mark",method:"post",params:e})}function u(e){return Object(i["a"])({url:"/admin/store/order/send",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:e})}function f(e){return Object(i["a"])({url:"/admin/store/order/refund",method:"get",params:e})}function p(e){return Object(i["a"])({url:"/admin/store/order/writeUpdate/".concat(e),method:"get"})}function v(e){return Object(i["a"])({url:"/admin/store/order/writeConfirm/".concat(e),method:"get"})}function h(){return Object(i["a"])({url:"/admin/store/order/statistics",method:"get"})}function b(e){return Object(i["a"])({url:"/admin/store/order/statisticsData",method:"get",params:e})}function g(e){return Object(i["a"])({url:"admin/store/order/update/price",method:"post",data:e})}function _(e){return Object(i["a"])({url:"/admin/store/order/time",method:"get",params:e})}function y(){return Object(i["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function w(e){return Object(i["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:e})}function x(){return Object(i["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function C(e){return Object(i["a"])({url:"/admin/store/order/video/send",method:"post",data:e})}function I(e){return Object(i["a"])({url:"/admin/yly/print/".concat(e),method:"get"})}},fa4e:function(e,t,r){"use strict";r("4324")},fb9d:function(e,t,r){var i={"./el-button.js":"64b1","./el-checkbox-group.js":"cc1a","./el-input.js":"373c","./el-radio-group.js":"afca","./el-select.js":"a938","./el-upload.js":"73ee"};function o(e){var t=a(e);return r(t)}function a(e){var t=i[e];if(!(t+1)){var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}return t}o.keys=function(){return Object.keys(i)},o.resolve=a,e.exports=o,o.id="fb9d"}}]);