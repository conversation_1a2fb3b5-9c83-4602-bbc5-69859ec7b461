(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b76d7688"],{"14c9":function(t,e,a){},bcaf:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"砍价状态："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[a("el-option",{attrs:{label:"关闭",value:0}}),t._v(" "),a("el-option",{attrs:{label:"开启",value:1}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称、ID",clearable:""},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1),t._v(" "),a("router-link",{attrs:{to:{path:"/marketing/bargain/creatBargain"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:save"],expression:"['admin:bargain:save']"}],staticClass:"mr10",attrs:{size:"mini",type:"primary"}},[t._v("添加砍价商品")])],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:export:excel:bargain"],expression:"['admin:export:excel:bargain']"}],staticClass:"mr10",attrs:{size:"mini"},on:{click:t.exportList}},[t._v("导出")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"砍价名称",prop:"title","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[a("div",{staticClass:"text_overflow",attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.title))]),t._v(" "),a("div",{staticClass:"pup_card"},[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"砍价价格",prop:"price","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"最低价",prop:"minPrice","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"参与人数",prop:"countPeopleAll","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"帮忙砍价人数",prop:"countPeopleHelp","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价成功人数",prop:"countPeopleSuccess","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"限量","min-width":"100",prop:"quotaShow",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"限量剩余",prop:"surplusQuota","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"stopTime",label:"活动时间","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.startTime+" ~ "+e.row.stopTime)+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"砍价状态","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.checkPermi(["admin:bargain:update:status"])?a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},on:{change:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}}):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{attrs:{to:{path:"/marketing/bargain/creatBargain/"+e.row.id}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:info"],expression:"['admin:bargain:info']"}],attrs:{type:"text",size:"small"}},[t._v("编辑")])],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:bargain:delete"],expression:"['admin:bargain:delete']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("div",{staticClass:"block mb20"},[t.checkPermi(["admin:bargain:list"])?a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}}):t._e()],1)],1)],1)},i=[],r=a("b7be"),o=a("e350");function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},n=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function r(a,n,i,r){var l=n&&n.prototype instanceof c?n:c,u=Object.create(l.prototype);return s(u,"_invoke",function(a,n,i){var r,l,s,c=0,u=i||[],m=!1,d={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,a){return r=e,l=0,s=t,d.n=a,o}};function p(a,n){for(l=a,s=n,e=0;!m&&c&&!i&&e<u.length;e++){var i,r=u[e],p=d.p,h=r[2];a>3?(i=h===n)&&(s=r[(l=r[4])?5:(l=3,3)],r[4]=r[5]=t):r[0]<=p&&((i=a<2&&p<r[1])?(l=0,d.v=n,d.n=r[1]):p<h&&(i=a<3||r[0]>n||n>h)&&(r[4]=a,r[5]=n,d.n=h,l=0))}if(i||a>1)return o;throw m=!0,n}return function(i,u,h){if(c>1)throw TypeError("Generator is already running");for(m&&1===u&&p(u,h),l=u,s=h;(e=l<2?t:s)||!m;){r||(l?l<3?(l>1&&(d.n=-1),p(l,s)):d.n=s:d.v=s);try{if(c=2,r){if(l||(i="next"),e=r[i]){if(!(e=e.call(r,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,l<2&&(l=0)}else 1===l&&(e=r.return)&&e.call(r),l<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),l=1);r=t}else if((e=(m=d.n<0)?s:a.call(n,d))!==o)break}catch(e){r=t,l=1,s=e}finally{c=1}}return{value:e,done:m}}}(a,i,r),!0),u}var o={};function c(){}function u(){}function m(){}e=Object.getPrototypeOf;var d=[][n]?e(e([][n]())):(s(e={},n,(function(){return this})),e),p=m.prototype=c.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,i,"GeneratorFunction")),t.prototype=Object.create(p),t}return u.prototype=m,s(p,"constructor",m),s(m,"constructor",u),u.displayName="GeneratorFunction",s(m,i,"GeneratorFunction"),s(p),s(p,i,"Generator"),s(p,n,(function(){return this})),s(p,"toString",(function(){return"[object Generator]"})),(l=function(){return{w:r,m:h}})()}function s(t,e,a,n){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}s=function(t,e,a,n){function r(e,a){s(t,e,(function(t){return this._invoke(e,a,t)}))}e?i?i(t,e,{value:a,enumerable:!n,configurable:!n,writable:!n}):t[e]=a:(r("next",0),r("throw",1),r("return",2))},s(t,e,a,n)}function c(t,e,a,n,i,r,o){try{var l=t[r](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(n,i)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function o(t){c(r,n,i,o,l,"next",t)}function l(t){c(r,n,i,o,l,"throw",t)}o(void 0)}))}}var m={name:"index",data:function(){return{tableFrom:{page:1,limit:20,keywords:"",status:null},listLoading:!0,tableData:{data:[],total:0},afterData:[]}},mounted:function(){var t=[{city_id:1,city_name:"北京",city_img:"http://dfknbdjknvkjsfnvlkjdn.png",city_country:"中国"},{city_id:2,city_name:"上海",city_img:"http://wergerbe.png",city_country:"中国"},{city_id:3,city_name:"广州",city_img:"http://hrthhr.png",city_country:"中国"},{city_id:4,city_name:"西雅图",city_img:"http://frevfd.png",city_country:"美国"},{city_id:5,city_name:"纽约",city_img:"http://反而个.png",city_country:"美国"}],e=[];t.forEach((function(t){var a={name:t.city_country,citys:[]},n={city_name:t.city_name,city_img:t.city_img,city_id:t.city_id};a.citys.push(n),e.push(a)}));var a=[],n={};e.forEach((function(t,i){n[t.name]?a.forEach((function(t){t.name===e[i].name&&(t.citys=t.citys.concat(e[i].citys))})):(a.push(t),n[t.name]=!0)})),this.getList()},methods:{checkPermi:o["a"],exportList:function(){Object(r["x"])({keywords:this.tableFrom.keywords,status:this.tableFrom.status}).then((function(t){window.open(t.fileName)}))},handleDelete:function(t,e){var a=this;this.$modal.confirm("确认删除该商品吗").then((function(){Object(r["a"])({id:t}).then((function(){a.$message.success("删除成功"),a.getList()}))})).catch((function(){}))},onchangeIsShow:function(t){var e=this;Object(r["g"])({id:t.id,status:t.status}).then(u(l().m((function t(){return l().w((function(t){while(1)switch(t.n){case 0:e.$message.success("修改成功"),e.getList();case 1:return t.a(2)}}),t)})))).catch((function(){t.status=!t.status}))},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(r["c"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},add:function(){this.isCreate=0,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.editData={}}}},d=m,p=(a("bcdc"),a("2877")),h=Object(p["a"])(d,n,i,!1,null,"4b9cab55",null);e["default"]=h.exports},bcdc:function(t,e,a){"use strict";a("14c9")}}]);