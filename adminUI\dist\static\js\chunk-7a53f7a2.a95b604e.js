(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7a53f7a2"],{4482:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"index",label:t.$t("common.serialNumber"),width:"110"}}),t._v(" "),a("el-table-column",{attrs:{label:t.$t("parameter.withdrawalFee.feeTemplateId"),"min-width":"80",prop:"id"}}),t._v(" "),a("el-table-column",{attrs:{label:t.$t("parameter.withdrawalFee.minWithdrawAmount"),"min-width":"150",prop:"min_withdraw_amount"}}),t._v(" "),a("el-table-column",{attrs:{label:t.$t("parameter.withdrawalFee.maxWithdrawAmount"),"min-width":"150",prop:"max_withdraw_amount"}}),t._v(" "),a("el-table-column",{attrs:{label:t.$t("parameter.withdrawalFee.withdrawFeeRate"),"min-width":"150",prop:"withdraw_fee_rate"}}),t._v(" "),a("el-table-column",{attrs:{fixed:"right",label:t.$t("parameter.withdrawalFee.operation"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v(t._s(t.$t("parameter.withdrawalFee.edit")))])]}}])})],1),t._v(" "),a("el-dialog",{attrs:{"append-to-body":"",visible:t.dialogFormVisible,title:t.dialogTitle,width:"680px"},on:{"update:visible":function(e){t.dialogFormVisible=e},close:t.handleCancle}},[a("el-form",{ref:"elForm",attrs:{inline:"",model:t.form,rules:t.rules,"label-width":"200px"}},[a("el-form-item",{attrs:{label:t.$t("parameter.withdrawalFee.feeTemplateId")+"："}},[a("el-input",{attrs:{size:"small",placeholder:t.$t("parameter.withdrawalFee.placeholder.couponId"),disabled:""},model:{value:t.form.id,callback:function(e){t.$set(t.form,"id",e)},expression:"form.id"}})],1),t._v(" "),a("el-form-item",{attrs:{label:t.$t("parameter.withdrawalFee.minWithdrawAmount")+"：",prop:"min_withdraw_amount"}},[a("el-input",{attrs:{size:"small",placeholder:t.$t("parameter.withdrawalFee.placeholder.minWithdrawAmount")},model:{value:t.form.min_withdraw_amount,callback:function(e){t.$set(t.form,"min_withdraw_amount",e)},expression:"form.min_withdraw_amount"}})],1),t._v(" "),a("el-form-item",{attrs:{label:t.$t("parameter.withdrawalFee.maxWithdrawAmount")+"：",prop:"max_withdraw_amount"}},[a("el-input",{attrs:{size:"small",placeholder:t.$t("parameter.withdrawalFee.placeholder.maxWithdrawAmount")},model:{value:t.form.max_withdraw_amount,callback:function(e){t.$set(t.form,"max_withdraw_amount",e)},expression:"form.max_withdraw_amount"}})],1),t._v(" "),a("el-form-item",{attrs:{label:t.$t("parameter.withdrawalFee.withdrawFeeRate")+"：",prop:"withdraw_fee_rate"}},[a("el-input",{attrs:{size:"small",placeholder:"0"},model:{value:t.form.withdraw_fee_rate,callback:function(e){t.$set(t.form,"withdraw_fee_rate",e)},expression:"form.withdraw_fee_rate"}})],1)],1),t._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.handelConfirm}},[t._v(t._s(t.$t("common.confirm")))]),t._v(" "),a("el-button",{on:{click:t.handleCancle}},[t._v(t._s(t.$t("common.cancel")))])],1)],1)],1)],1)},i=[],l=a("fb04"),o={name:"WithdrawalFees",data:function(){return{loading:!1,searchFrom:{formId:103},tableData:[],dialogTitle:this.$t("parameter.withdrawalFee.addTitle"),dialogFormVisible:!1,form:{couponId:"",min_withdraw_amount:"",max_withdraw_amount:"",withdraw_fee_rate:0},rules:{min_withdraw_amount:[{required:!0,message:this.$t("parameter.withdrawalFee.placeholder.minWithdrawAmount"),trigger:"blur"}],max_withdraw_amount:[{required:!0,message:this.$t("parameter.withdrawalFee.placeholder.maxWithdrawAmount"),trigger:"blur"}],withdraw_fee_rate:[{required:!0,message:this.$t("parameter.withdrawalFee.placeholder.withdrawFeeRate"),trigger:"blur"}]}}},created:function(){},mounted:function(){this.getList()},methods:{getList:function(t){var e=this;this.loading=!0,Object(l["a"])(this.searchFrom).then((function(t){t&&(e.tableData=[t]),e.loading=!1})).catch((function(){e.loading=!1}))},handleEdit:function(t){this.dialogTitle=this.$t("parameter.withdrawalFee.editTitle"),this.dialogFormVisible=!0,this.form.id=t.id,this.form.min_withdraw_amount=t.min_withdraw_amount,this.form.max_withdraw_amount=t.max_withdraw_amount,this.form.withdraw_fee_rate=t.withdraw_fee_rate},handleCancle:function(){this.form={id:"",min_withdraw_amount:"",max_withdraw_amount:"",withdraw_fee_rate:0},this.dialogFormVisible=!1},handelConfirm:function(){var t=this;this.$refs.elForm.validate((function(e){if(e){var a={id:t.form.id,sort:1,status:!0,fields:[{name:"min_withdraw_amount",value:t.form.min_withdraw_amount,title:"min_withdraw_amount"},{name:"max_withdraw_amount",value:t.form.max_withdraw_amount,title:"max_withdraw_amount"},{name:"withdraw_fee_rate",value:t.form.withdraw_fee_rate,title:"withdraw_fee_rate"}]};Object(l["b"])(a).then((function(e){t.$message.success(t.$t("common.operationSuccess")),t.handleCancle(),t.getList()}))}}))}}},n=o,m=a("2877"),d=Object(m["a"])(n,r,i,!1,null,"74fb6856",null);e["default"]=d.exports},fb04:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return l}));var r=a("b775");function i(t){return Object(r["a"])({url:"/admin/system/config/info",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/admin/system/config/save/form",method:"post",data:t})}}}]);