(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-747e3bd0"],{"9add":function(t,e,n){"use strict";n.d(e,"f",(function(){return r})),n.d(e,"j",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"e",(function(){return l})),n.d(e,"h",(function(){return s})),n.d(e,"i",(function(){return c})),n.d(e,"g",(function(){return u})),n.d(e,"c",(function(){return d})),n.d(e,"d",(function(){return m})),n.d(e,"b",(function(){return f})),n.d(e,"k",(function(){return p}));var a=n("b775");function r(t){return Object(a["a"])({url:"/admin/brand/list",method:"GET",params:t})}function o(t){return Object(a["a"])({url:"/admin/brand/update",method:"POST",data:t})}function i(t){return Object(a["a"])({url:"/admin/brand/add",method:"POST",data:t})}function l(t){return Object(a["a"])({url:"/admin/brand/batchUpdate",method:"POST",data:t})}function s(t){return Object(a["a"])({url:"/admin/system/group/data/list?gid="+t,method:"GET"})}function c(t){return Object(a["a"])({url:"/admin/store/product/list",method:"GET",params:t})}function u(t,e){return Object(a["a"])({url:"/admin/store/product/importProduct?form="+t+"&url="+e,method:"POST"})}function d(t){return Object(a["a"])({url:"/admin/store/product/batch/putOnShell",method:"POST",data:t})}function m(t){return Object(a["a"])({url:"/admin/store/product/batch/offShell",method:"POST",data:t})}function f(t){return Object(a["a"])({url:"/admin/store/product/batch/delete",method:"POST",data:t})}function p(t){return Object(a["a"])({url:"/admin/store/product/update",method:"POST",data:t})}},cb99:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"divBox relative"},[n("el-card",{staticClass:"box-card"},[n("el-tabs",{staticClass:"mb20",on:{"tab-click":t.onChangeType},model:{value:t.form.type,callback:function(e){t.$set(t.form,"type",e)},expression:"form.type"}},[n("el-tab-pane",{attrs:{label:t.$t("product.isHot"),name:"1"}}),t._v(" "),n("el-tab-pane",{attrs:{label:t.$t("product.isBenefit"),name:"2"}}),t._v(" "),n("el-tab-pane",{attrs:{label:t.$t("product.isTikTok"),name:"3"}})],1),t._v(" "),n("div",{staticClass:"container mt-1"},[n("el-form",{attrs:{inline:"",size:"small"},model:{value:t.form,callback:function(e){t.form=e},expression:"form"}},[n("el-form-item",{attrs:{label:t.$t("product.productName")}},[n("el-input",{attrs:{placeholder:t.$t("product.enterProductName"),clearable:""},model:{value:t.form.keywords,callback:function(e){t.$set(t.form,"keywords",e)},expression:"form.keywords"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("product.status")}},[n("el-select",{attrs:{placeholder:t.$t("product.pleaseSelect")},model:{value:t.form.isShow,callback:function(e){t.$set(t.form,"isShow",e)},expression:"form.isShow"}},t._l(t.statusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:t.$t("product."+e.label),value:e.value}})})),1)],1)],1)],1),t._v(" "),n("el-button",{staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:t.getList}},[t._v("\n      "+t._s(t.$t("product.query"))+"\n    ")]),t._v(" "),n("el-button",{staticClass:"mr10",attrs:{size:"small",type:""},on:{click:function(e){return t.resetForm()}}},[t._v("\n      "+t._s(t.$t("product.reset"))+"\n    ")])],1),t._v(" "),n("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"12px"}},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[n("el-table-column",{attrs:{label:t.$t("common.serialNumber"),type:"index",width:"110"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.productImage"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("div",{staticClass:"demo-image__preview"},[n("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.productName"),prop:"storeName","min-width":"300","show-overflow-tooltip":!0}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.productPrice"),"min-width":"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.formatAmount(e.row.price)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.cashbackRate"),"min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.formatRate(e.row.cashBackRate)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.addTime"),"min-width":"120",align:"center",prop:"addTime"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.formatTime(e.row.addTime)))]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.status"),"min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tag",{attrs:{type:e.row.isShow?"success":"danger",size:"small"}},[t._v("\n            "+t._s(e.row.isShow?t.$t("product.online"):t.$t("product.offline"))+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.action"),"min-width":"60",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isShow?n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(n){return t.handleDelete(e.row)}}},[t._v("\n            "+t._s(t.$t("product.offline"))+"\n          ")]):n("span",{staticClass:"text-muted"},[t._v("-")])]}}])})],1),t._v(" "),n("el-pagination",{staticClass:"mt20",attrs:{"current-page":t.form.page,"page-sizes":[20,40,60,100],"page-size":t.form.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.form.total},on:{"size-change":t.sizeChange,"current-change":t.pageChange}})],1)],1)},r=[],o=n("73f5"),i=n("9add"),l={name:"AppHome",data:function(){return{statusOptions:[{value:-1,label:this.$t("all")},{value:1,label:this.$t("online")},{value:0,label:this.$t("offline")}],form:{keywords:"",page:1,limit:20,type:"1",isShow:""},tableData:[],levelList:[],levelData:[],loading:!1}},created:function(){},mounted:function(){this.getList()},methods:{formatAmount:function(t){void 0==t&&(t=0);var e=(t/1e3).toFixed(3);return e},onChangeType:function(){this.getList()},getList:function(t){var e=this;this.form.page=1,this.loading=!0,Object(o["o"])(this.form).then((function(t){e.tableData=t.list,e.form.total=t.total,e.loading=!1})).catch((function(){e.loading=!1}))},pageChange:function(t){this.form.page=t,this.getList()},sizeChange:function(t){this.form.limit=t,this.getList()},resetForm:function(){this.form.keywords="",this.form.isShow="",this.form.page=1,this.form.limit=20,this.getList()},formatTime:function(t){var e=new Date(1e3*t),n=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),i=String(e.getMinutes()).padStart(2,"0"),l=String(e.getSeconds()).padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(r," ").concat(o,":").concat(i,":").concat(l)},handleUpdate:function(t,e){var n=this;this.$confirm(this.$t("brand.confirmOperation"),this.$t("brand.prompt"),{confirmButtonText:this.$t("brand.confirm"),cancelButtonText:this.$t("brand.cancel"),type:"warning",showClose:!1}).then((function(){var e={ids:[t.id]};t.isShow?Object(i["d"])(e).then((function(t){n.getList()})):Object(i["c"])(e).then((function(t){n.getList()}))}))},formatRate:function(t){return parseInt(1e4*t)/100+"%"},handleDelete:function(t){var e=this,n={id:t.id};1==this.form.type?n["isHot"]=!1:2==this.form.type?n["isBenefit"]=!1:3==this.form.type&&(n["isBest"]=!1),this.$confirm(this.$t("brand.confirmOperation"),this.$t("brand.prompt"),{confirmButtonText:this.$t("brand.confirm"),cancelButtonText:this.$t("brand.cancel"),type:"warning",showClose:!1}).then((function(){e.loading=!0,Object(i["k"])(n).then((function(t){e.getList()})).catch((function(t){}))}))}}},s=l,c=(n("de0d"),n("2877")),u=Object(c["a"])(s,a,r,!1,null,"3566cf80",null);e["default"]=u.exports},de0d:function(t,e,n){"use strict";n("f52c")},f52c:function(t,e,n){}}]);